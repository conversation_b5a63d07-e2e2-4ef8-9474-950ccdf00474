using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Analytics;
using Core.CrossCuttingConcerns.Caching.Analytics.Models;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache operasyonlarını analiz için kaydeden aspect
    /// </summary>
    public class CacheAnalyticsAspect : MethodInterception
    {
        private readonly ICacheAnalyticsService _analyticsService;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ICompanyContext _companyContext;
        private readonly ICacheManager _cacheManager;

        public CacheAnalyticsAspect()
        {
            _analyticsService = ServiceTool.ServiceProvider.GetService<ICacheAnalyticsService>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
        }

        public override void Intercept(IInvocation invocation)
        {
            var stopwatch = Stopwatch.StartNew();
            var tenantId = GetTenantId();
            var className = invocation.Method.ReflectedType?.FullName ?? "Unknown";
            var methodName = invocation.Method.Name;
            var entityName = ExtractEntityName(className);
            
            string cacheKey = null;
            bool isHit = false;
            long memoryImpact = 0;

            try
            {
                // Cache key oluştur
                if (tenantId > 0)
                {
                    var arguments = invocation.Arguments ?? Array.Empty<object>();
                    cacheKey = _keyGenerator.GenerateKey(tenantId, className, methodName, arguments);
                    
                    // Cache'de var mı kontrol et
                    isHit = _cacheManager.IsAdd(cacheKey);
                }

                // Metodu çalıştır
                invocation.Proceed();

                // Memory impact hesapla
                if (!string.IsNullOrEmpty(cacheKey) && invocation.ReturnValue != null)
                {
                    memoryImpact = EstimateMemoryImpact(invocation.ReturnValue);
                }
            }
            catch (Exception ex)
            {
                // Hata durumunda da kaydet
                System.Diagnostics.Debug.WriteLine($"CacheAnalyticsAspect Error: {ex.Message}");
            }
            finally
            {
                stopwatch.Stop();

                // Analytics için operation kaydı oluştur
                if (tenantId > 0 && !string.IsNullOrEmpty(cacheKey))
                {
                    var operation = new CacheOperation
                    {
                        Timestamp = DateTime.UtcNow,
                        TenantId = tenantId,
                        EntityName = entityName,
                        OperationType = methodName,
                        CacheKey = cacheKey,
                        IsHit = isHit,
                        ResponseTimeMs = stopwatch.ElapsedMilliseconds,
                        MemoryImpact = memoryImpact,
                        Metadata = new System.Collections.Generic.Dictionary<string, object>
                        {
                            ["ClassName"] = className,
                            ["MethodName"] = methodName,
                            ["ArgumentCount"] = invocation.Arguments?.Length ?? 0,
                            ["HasReturnValue"] = invocation.ReturnValue != null
                        }
                    };

                    // Async olarak kaydet (performance için)
                    Task.Run(async () =>
                    {
                        try
                        {
                            await _analyticsService.RecordCacheOperationAsync(operation);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Analytics recording error: {ex.Message}");
                        }
                    });
                }
            }
        }

        private int GetTenantId()
        {
            try
            {
                return _companyContext?.GetCompanyId() ?? -1;
            }
            catch
            {
                return -1;
            }
        }

        private string ExtractEntityName(string className)
        {
            if (string.IsNullOrEmpty(className))
                return "Unknown";

            var parts = className.Split('.');
            var managerName = parts[^1];

            if (managerName.EndsWith("Manager"))
                return managerName[..^7];

            if (managerName.EndsWith("Service"))
                return managerName[..^7];

            return managerName;
        }

        private long EstimateMemoryImpact(object value)
        {
            try
            {
                if (value == null) return 0;

                // Basit memory impact tahmini
                var type = value.GetType();

                // Primitive types
                if (type.IsPrimitive)
                {
                    return type == typeof(bool) ? 1 :
                           type == typeof(byte) ? 1 :
                           type == typeof(char) ? 2 :
                           type == typeof(short) ? 2 :
                           type == typeof(int) ? 4 :
                           type == typeof(long) ? 8 :
                           type == typeof(float) ? 4 :
                           type == typeof(double) ? 8 : 8;
                }

                // String
                if (type == typeof(string))
                {
                    return ((string)value).Length * 2 + 24; // Unicode + overhead
                }

                // Collections
                if (value is System.Collections.IEnumerable enumerable && !(value is string))
                {
                    long size = 32; // Collection overhead
                    int count = 0;
                    foreach (var item in enumerable)
                    {
                        size += EstimateMemoryImpact(item);
                        count++;
                        if (count > 100) break; // Performance limit
                    }
                    return size;
                }

                // Complex objects - JSON serialization size as approximation
                try
                {
                    var json = System.Text.Json.JsonSerializer.Serialize(value);
                    return System.Text.Encoding.UTF8.GetByteCount(json);
                }
                catch
                {
                    // Fallback: estimate based on property count
                    var properties = type.GetProperties();
                    return properties.Length * 50 + 100; // Rough estimate
                }
            }
            catch
            {
                return 1024; // Default 1KB estimate
            }
        }
    }

    /// <summary>
    /// Sadece cache hit/miss tracking için lightweight aspect
    /// </summary>
    public class CacheTrackingAspect : MethodInterception
    {
        private readonly ICacheAnalyticsService _analyticsService;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ICompanyContext _companyContext;
        private readonly ICacheManager _cacheManager;

        public CacheTrackingAspect()
        {
            _analyticsService = ServiceTool.ServiceProvider.GetService<ICacheAnalyticsService>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
        }

        public override void Intercept(IInvocation invocation)
        {
            var tenantId = GetTenantId();
            var className = invocation.Method.ReflectedType?.FullName ?? "Unknown";
            var methodName = invocation.Method.Name;
            var entityName = ExtractEntityName(className);
            
            bool isHit = false;
            string cacheKey = null;

            if (tenantId > 0)
            {
                var arguments = invocation.Arguments ?? Array.Empty<object>();
                cacheKey = _keyGenerator.GenerateKey(tenantId, className, methodName, arguments);
                isHit = _cacheManager.IsAdd(cacheKey);
            }

            invocation.Proceed();

            // Lightweight tracking - sadece hit/miss
            if (tenantId > 0 && !string.IsNullOrEmpty(cacheKey))
            {
                var operation = new CacheOperation
                {
                    Timestamp = DateTime.UtcNow,
                    TenantId = tenantId,
                    EntityName = entityName,
                    OperationType = methodName,
                    CacheKey = cacheKey,
                    IsHit = isHit,
                    ResponseTimeMs = 0, // Skip timing for performance
                    MemoryImpact = 0    // Skip memory calculation for performance
                };

                // Fire and forget
                Task.Run(async () =>
                {
                    try
                    {
                        await _analyticsService.RecordCacheOperationAsync(operation);
                    }
                    catch
                    {
                        // Ignore errors in tracking
                    }
                });
            }
        }

        private int GetTenantId()
        {
            try
            {
                return _companyContext?.GetCompanyId() ?? -1;
            }
            catch
            {
                return -1;
            }
        }

        private string ExtractEntityName(string className)
        {
            if (string.IsNullOrEmpty(className))
                return "Unknown";

            var parts = className.Split('.');
            var managerName = parts[^1];

            if (managerName.EndsWith("Manager"))
                return managerName[..^7];

            if (managerName.EndsWith("Service"))
                return managerName[..^7];

            return managerName;
        }
    }
}
