import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { SingleResponseModel, ResponseModel } from '../models/responseModel';

// Analytics Models
export interface CacheAnalyticsSnapshot {
  timestamp: string;
  totalMemoryUsage: number;
  cacheMemoryUsage: number;
  memoryPressureLevel: number;
  totalKeys: number;
  activeTenants: number;
  overallHitRatio: number;
  tenantSnapshots: { [key: number]: TenantSnapshot };
  entitySnapshots: { [key: string]: EntitySnapshot };
}

export interface TenantSnapshot {
  tenantId: number;
  memoryUsage: number;
  keyCount: number;
  hitRatio: number;
  lastActivity: string;
  entityCounts: { [key: string]: number };
}

export interface EntitySnapshot {
  entityName: string;
  totalMemoryUsage: number;
  totalKeys: number;
  averageHitRatio: number;
  averageDuration: string;
  tenantsUsingEntity: number;
}

export interface CacheUsagePattern {
  tenantId: number;
  entityName: string;
  operationType: string;
  timeWindow: string;
  operationCount: number;
  hitRatio: number;
  averageResponseTime: number;
  memoryImpact: number;
}

export interface MemoryUsageAnalysis {
  analysisTime: string;
  totalSystemMemory: number;
  cacheMemoryUsage: number;
  memoryPressurePercentage: number;
  pressureLevel: string;
  hotspots: MemoryHotspot[];
  trend: MemoryTrend;
  estimatedOptimalSize: number;
}

export interface MemoryHotspot {
  tenantId: number;
  entityName: string;
  memoryUsage: number;
  percentageOfTotal: number;
  keyCount: number;
  efficiencyScore: number;
}

export interface MemoryTrend {
  direction: 'Stable' | 'Growing' | 'Declining' | 'Volatile';
  growthRate: number;
  predictedFullCapacity: string;
  dataPoints: MemoryDataPoint[];
}

export interface MemoryDataPoint {
  timestamp: string;
  memoryUsage: number;
  keyCount: number;
}

export interface CacheMetricsHistory {
  timestamp: string;
  memoryUsage: number;
  keyCount: number;
  hitRatio: number;
  activeTenants: number;
  memoryPressure: number;
  additionalMetrics: { [key: string]: any };
}

export interface TenantCacheAnalysis {
  tenantId: number;
  analysisPeriod: string;
  averageMemoryUsage: number;
  peakMemoryUsage: number;
  averageHitRatio: number;
  totalOperations: number;
  entityStats: { [key: string]: EntityUsageStats };
  usagePatterns: CacheUsagePattern[];
  healthScore: TenantHealthScore;
}

export interface EntityUsageStats {
  entityName: string;
  memoryUsage: number;
  keyCount: number;
  hitRatio: number;
  accessCount: number;
  averageDuration: string;
  efficiencyScore: number;
}

export interface TenantHealthScore {
  overallScore: number;
  memoryEfficiency: number;
  cacheHitRatio: number;
  configurationOptimization: number;
  issues: string[];
  recommendations: string[];
}

export interface CacheHotspot {
  identifier: string;
  type: 'HighMemoryUsage' | 'LowHitRatio' | 'FrequentAccess' | 'LongDuration' | 'IneffectiveConfiguration';
  tenantId: number;
  entityName: string;
  memoryUsage: number;
  accessFrequency: number;
  impactScore: number;
  description: string;
  recommendations: string[];
}

export interface CacheInefficiency {
  type: string;
  tenantId: number;
  entityName: string;
  description: string;
  impactScore: number;
  wastedMemory: number;
  recommendation: string;
  potentialSavings: number;
}

export interface CachePrediction {
  predictionTime: string;
  forecastPeriod: string;
  predictedMemoryUsage: number;
  predictedKeyCount: number;
  confidenceLevel: number;
  tenantPredictions: TenantPrediction[];
  assumptions: string[];
  riskFactors: string[];
}

export interface TenantPrediction {
  tenantId: number;
  predictedMemoryUsage: number;
  predictedKeyCount: number;
  growthRate: number;
  riskLevel: string;
}

export interface CacheOptimizationRecommendation {
  id: string;
  type: 'DurationAdjustment' | 'MemoryLimit' | 'EvictionPolicy' | 'ConfigurationChange' | 'ArchitecturalChange' | 'DataStructureOptimization';
  title: string;
  description: string;
  priority: number;
  impactScore: number;
  estimatedMemorySavings: number;
  estimatedPerformanceGain: number;
  implementation: string;
  prerequisites: string[];
  parameters: { [key: string]: any };
}

export interface CacheHealthScore {
  overallScore: number;
  calculatedAt: string;
  breakdown: HealthScoreBreakdown;
  issues: HealthIssue[];
  recommendations: HealthRecommendation[];
  healthLevel: string;
}

export interface HealthScoreBreakdown {
  memoryEfficiency: number;
  hitRatioScore: number;
  configurationScore: number;
  performanceScore: number;
  stabilityScore: number;
}

export interface HealthIssue {
  type: string;
  description: string;
  severity: string;
  impact: number;
  recommendation: string;
}

export interface HealthRecommendation {
  action: string;
  reason: string;
  priority: number;
  expectedImprovement: number;
}

export interface CacheConfigurationAnalysis {
  analysisTime: string;
  issues: ConfigurationIssue[];
  recommendations: ConfigurationRecommendation[];
  entityAnalysis: { [key: string]: EntityConfigAnalysis };
  optimizationScore: number;
}

export interface ConfigurationIssue {
  entityName: string;
  issueType: string;
  description: string;
  severity: string;
  impact: number;
}

export interface ConfigurationRecommendation {
  entityName: string;
  parameter: string;
  currentValue: any;
  recommendedValue: any;
  reason: string;
  expectedImprovement: number;
}

export interface EntityConfigAnalysis {
  entityName: string;
  currentDuration: number;
  recommendedDuration: number;
  memoryEfficiency: number;
  hitRatio: number;
  currentTags: string[];
  recommendedTags: string[];
  isOptimal: boolean;
}

export interface EntityCacheAnalysis {
  entityName: string;
  totalMemoryUsage: number;
  totalKeys: number;
  averageHitRatio: number;
  tenantsUsing: number;
  averageDuration: string;
  efficiencyScore: number;
  tenantUsages: TenantEntityUsage[];
  optimizationPotential: EntityOptimizationPotential;
}

export interface TenantEntityUsage {
  tenantId: number;
  memoryUsage: number;
  keyCount: number;
  hitRatio: number;
  accessCount: number;
}

export interface EntityOptimizationPotential {
  memorySavingsPotential: number;
  performanceGainPotential: number;
  optimizationActions: string[];
  priority: number;
}

export interface AnalyticsDashboard {
  snapshot: CacheAnalyticsSnapshot;
  memoryAnalysis: MemoryUsageAnalysis;
  healthScore: CacheHealthScore;
  hotspots: CacheHotspot[];
  recommendations: CacheOptimizationRecommendation[];
}

export interface RealTimeMetrics {
  timestamp: string;
  memoryUsage: number;
  memoryPressure: number;
  pressureLevel: string;
  totalKeys: number;
  activeTenants: number;
  hitRatio: number;
  trend: string;
}

@Injectable({
  providedIn: 'root'
})
export class CacheAnalyticsService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  // Snapshot ve real-time data
  getCurrentSnapshot(): Observable<SingleResponseModel<CacheAnalyticsSnapshot>> {
    return this.httpClient.get<SingleResponseModel<CacheAnalyticsSnapshot>>(
      `${this.apiUrl}cacheanalytics/snapshot`
    );
  }

  getRealTimeMetrics(): Observable<SingleResponseModel<RealTimeMetrics>> {
    return this.httpClient.get<SingleResponseModel<RealTimeMetrics>>(
      `${this.apiUrl}cacheanalytics/real-time-metrics`
    );
  }

  // Usage patterns ve analysis
  getUsagePatterns(tenantId: number, hours: number = 24): Observable<SingleResponseModel<CacheUsagePattern[]>> {
    return this.httpClient.get<SingleResponseModel<CacheUsagePattern[]>>(
      `${this.apiUrl}cacheanalytics/usage-patterns/${tenantId}?hours=${hours}`
    );
  }

  getMemoryUsageAnalysis(): Observable<SingleResponseModel<MemoryUsageAnalysis>> {
    return this.httpClient.get<SingleResponseModel<MemoryUsageAnalysis>>(
      `${this.apiUrl}cacheanalytics/memory-analysis`
    );
  }

  // Historical data
  getHistoricalData(startDate?: Date, endDate?: Date): Observable<SingleResponseModel<CacheMetricsHistory[]>> {
    let url = `${this.apiUrl}cacheanalytics/historical`;
    const params = [];
    
    if (startDate) {
      params.push(`startDate=${startDate.toISOString()}`);
    }
    if (endDate) {
      params.push(`endDate=${endDate.toISOString()}`);
    }
    
    if (params.length > 0) {
      url += '?' + params.join('&');
    }
    
    return this.httpClient.get<SingleResponseModel<CacheMetricsHistory[]>>(url);
  }

  // Tenant analysis
  getTenantAnalysis(tenantId: number, hours: number = 24): Observable<SingleResponseModel<TenantCacheAnalysis>> {
    return this.httpClient.get<SingleResponseModel<TenantCacheAnalysis>>(
      `${this.apiUrl}cacheanalytics/tenant-analysis/${tenantId}?hours=${hours}`
    );
  }

  // Hotspots ve inefficiencies
  getCacheHotspots(): Observable<SingleResponseModel<CacheHotspot[]>> {
    return this.httpClient.get<SingleResponseModel<CacheHotspot[]>>(
      `${this.apiUrl}cacheanalytics/hotspots`
    );
  }

  getInefficiencies(): Observable<SingleResponseModel<CacheInefficiency[]>> {
    return this.httpClient.get<SingleResponseModel<CacheInefficiency[]>>(
      `${this.apiUrl}cacheanalytics/inefficiencies`
    );
  }

  // Predictions
  getMemoryPredictions(forecastHours: number = 24): Observable<SingleResponseModel<CachePrediction>> {
    return this.httpClient.get<SingleResponseModel<CachePrediction>>(
      `${this.apiUrl}cacheanalytics/predictions?forecastHours=${forecastHours}`
    );
  }

  // Recommendations ve health
  getOptimizationRecommendations(): Observable<SingleResponseModel<CacheOptimizationRecommendation[]>> {
    return this.httpClient.get<SingleResponseModel<CacheOptimizationRecommendation[]>>(
      `${this.apiUrl}cacheanalytics/recommendations`
    );
  }

  getCacheHealthScore(): Observable<SingleResponseModel<CacheHealthScore>> {
    return this.httpClient.get<SingleResponseModel<CacheHealthScore>>(
      `${this.apiUrl}cacheanalytics/health-score`
    );
  }

  // Configuration analysis
  getConfigurationAnalysis(): Observable<SingleResponseModel<CacheConfigurationAnalysis>> {
    return this.httpClient.get<SingleResponseModel<CacheConfigurationAnalysis>>(
      `${this.apiUrl}cacheanalytics/configuration-analysis`
    );
  }

  getEntityAnalysis(): Observable<SingleResponseModel<EntityCacheAnalysis[]>> {
    return this.httpClient.get<SingleResponseModel<EntityCacheAnalysis[]>>(
      `${this.apiUrl}cacheanalytics/entity-analysis`
    );
  }

  // Dashboard
  getAnalyticsDashboard(): Observable<SingleResponseModel<AnalyticsDashboard>> {
    return this.httpClient.get<SingleResponseModel<AnalyticsDashboard>>(
      `${this.apiUrl}cacheanalytics/dashboard`
    );
  }

  // Data management
  recordMemorySnapshot(): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheanalytics/record-snapshot`, {}
    );
  }

  cleanupOldData(retentionDays: number = 7): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheanalytics/cleanup?retentionDays=${retentionDays}`, {}
    );
  }

  // Utility methods
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatPercentage(value: number): string {
    return (value * 100).toFixed(1) + '%';
  }

  getHealthLevelColor(healthLevel: string): string {
    switch (healthLevel) {
      case 'Excellent': return 'text-success';
      case 'Good': return 'text-info';
      case 'Fair': return 'text-warning';
      case 'Poor': return 'text-danger';
      case 'Critical': return 'text-danger fw-bold';
      default: return 'text-secondary';
    }
  }

  getPressureLevelColor(pressureLevel: string): string {
    switch (pressureLevel) {
      case 'Low': return 'text-success';
      case 'Normal': return 'text-info';
      case 'Medium': return 'text-warning';
      case 'High': return 'text-danger';
      case 'Critical': return 'text-danger fw-bold';
      default: return 'text-secondary';
    }
  }

  getTrendColor(trend: string): string {
    switch (trend) {
      case 'Stable': return 'text-success';
      case 'Growing': return 'text-warning';
      case 'Declining': return 'text-info';
      case 'Volatile': return 'text-danger';
      default: return 'text-secondary';
    }
  }
}
