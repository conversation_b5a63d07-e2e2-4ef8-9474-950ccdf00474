using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.Isolation;
using Core.CrossCuttingConcerns.Caching.Isolation.Models;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.CrossCuttingConcerns.Caching.Models;
using Core.Utilities.IoC;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Core.CrossCuttingConcerns.Caching.MultiTenant
{
    public class MultiTenantCacheManager : ICacheManager
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly CacheConfiguration _configuration;
        private readonly CacheStatistics _statistics;
        private readonly ConcurrentDictionary<string, CacheItem> _cacheItems;
        private readonly ConcurrentDictionary<string, HashSet<string>> _tagIndex;
        private readonly object _lockObject = new object();
        private ITenantCacheIsolationManager _isolationManager;

        public MultiTenantCacheManager()
        {
            _memoryCache = ServiceTool.ServiceProvider.GetService<IMemoryCache>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _configuration = ServiceTool.ServiceProvider.GetService<CacheConfiguration>() ?? new CacheConfiguration();
            _statistics = new CacheStatistics();
            _cacheItems = new ConcurrentDictionary<string, CacheItem>();
            _tagIndex = new ConcurrentDictionary<string, HashSet<string>>();

            // Lazy initialization to avoid circular dependency
            InitializeIsolationManager();
        }

        private void InitializeIsolationManager()
        {
            try
            {
                _isolationManager = ServiceTool.ServiceProvider.GetService<ITenantCacheIsolationManager>();
            }
            catch
            {
                // Isolation manager may not be available during startup
                _isolationManager = null;
            }
        }

        public T Get<T>(string key)
        {
            try
            {
                var tenantId = _keyGenerator.ExtractTenantId(key);
                var entityName = _keyGenerator.ExtractEntityName(key);

                if (_memoryCache.TryGetValue(key, out T value))
                {
                    _statistics.RecordHit(tenantId, entityName);
                    LogDebug($"Cache HIT: {key}");
                    return value;
                }

                _statistics.RecordMiss(tenantId, entityName);
                LogDebug($"Cache MISS: {key}");
                return default(T);
            }
            catch (Exception ex)
            {
                LogDebug($"Cache GET Error: {key}, Error: {ex.Message}");
                return default(T);
            }
        }

        public object Get(string key)
        {
            return Get<object>(key);
        }

        public void Add(string key, object value, int durationMinutes)
        {
            Add(key, value, durationMinutes, null);
        }

        public void Add(string key, object value, int durationMinutes, string[] tags)
        {
            try
            {
                var tenantId = _keyGenerator.ExtractTenantId(key);
                var entityName = _keyGenerator.ExtractEntityName(key);

                // Check tenant isolation status
                if (await IsTenantIsolatedAsync(tenantId))
                {
                    LogDebug($"Cache add blocked for isolated tenant {tenantId}, key: {key}");
                    return;
                }

                // Check tenant quota before adding
                var estimatedSize = EstimateObjectSize(key, value);
                if (!await CheckTenantQuotaAsync(tenantId, estimatedSize))
                {
                    LogDebug($"Cache add blocked due to quota violation for tenant {tenantId}, key: {key}");
                    return;
                }

                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(durationMinutes),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, value, options);

                // Cache item bilgilerini sakla
                var cacheItem = new CacheItem
                {
                    Key = key,
                    TenantId = tenantId,
                    EntityName = entityName,
                    Tags = tags ?? Array.Empty<string>(),
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(durationMinutes)
                };

                _cacheItems.TryAdd(key, cacheItem);

                // Tag index'i güncelle
                UpdateTagIndex(key, cacheItem.Tags);

                _statistics.RecordSet(tenantId, entityName);
                LogDebug($"Cache SET: {key}, Duration: {durationMinutes}min, Tags: [{string.Join(", ", tags ?? Array.Empty<string>())}]");
            }
            catch (Exception ex)
            {
                LogDebug($"Cache SET Error: {key}, Error: {ex.Message}");
            }
        }

        public bool IsAdd(string key)
        {
            return _memoryCache.TryGetValue(key, out _);
        }

        public void Remove(string key)
        {
            try
            {
                _memoryCache.Remove(key);

                if (_cacheItems.TryRemove(key, out var cacheItem))
                {
                    RemoveFromTagIndex(key, cacheItem.Tags);
                    _statistics.RecordRemoval(cacheItem.TenantId, cacheItem.EntityName);
                    LogDebug($"Cache REMOVE: {key}");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Cache REMOVE Error: {key}, Error: {ex.Message}");
            }
        }

        public void RemoveByPattern(string pattern)
        {
            try
            {
                var keysToRemove = GetKeysByPattern(pattern);
                LogDebug($"Cache REMOVE BY PATTERN: {pattern}, Found {keysToRemove.Length} keys");

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Cache REMOVE BY PATTERN Error: {pattern}, Error: {ex.Message}");
            }
        }

        public void RemoveByTags(string[] tags)
        {
            try
            {
                var keysToRemove = new HashSet<string>();

                foreach (var tag in tags)
                {
                    if (_tagIndex.TryGetValue(tag, out var taggedKeys))
                    {
                        foreach (var key in taggedKeys)
                        {
                            keysToRemove.Add(key);
                        }
                    }
                }

                LogDebug($"Cache REMOVE BY TAGS: [{string.Join(", ", tags)}], Found {keysToRemove.Count} keys");

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Cache REMOVE BY TAGS Error: [{string.Join(", ", tags)}], Error: {ex.Message}");
            }
        }

        public void RemoveByTenant(int tenantId)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId);
            RemoveByPattern(pattern);
        }

        public void RemoveByEntity(int tenantId, string entityName)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId, entityName);
            RemoveByPattern(pattern);
        }

        public void RemoveByEntityAndOperation(int tenantId, string entityName, string operation)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId, entityName, operation);
            RemoveByPattern(pattern);
        }

        public Dictionary<string, T> GetMultiple<T>(string[] keys)
        {
            var result = new Dictionary<string, T>();
            foreach (var key in keys)
            {
                var value = Get<T>(key);
                if (value != null)
                {
                    result[key] = value;
                }
            }
            return result;
        }

        public void AddMultiple(Dictionary<string, object> items, int durationMinutes)
        {
            foreach (var item in items)
            {
                Add(item.Key, item.Value, durationMinutes);
            }
        }

        public void RemoveMultiple(string[] keys)
        {
            foreach (var key in keys)
            {
                Remove(key);
            }
        }

        public void Clear()
        {
            lock (_lockObject)
            {
                _cacheItems.Clear();
                _tagIndex.Clear();

                // IMemoryCache'in Clear metodu yok, yeni instance oluşturmak gerekiyor
                // Bunun yerine tüm key'leri tek tek siliyoruz
                var allKeys = GetAllKeys();
                foreach (var key in allKeys)
                {
                    _memoryCache.Remove(key);
                }

                LogDebug("Cache CLEARED");
            }
        }

        public void ClearTenant(int tenantId)
        {
            RemoveByTenant(tenantId);
        }

        public long GetCacheSize()
        {
            // Gerçek bellek boyutunu döndür, item sayısını değil
            return CalculateActualCacheMemoryUsage();
        }

        public long GetCacheItemCount()
        {
            return _cacheItems.Count;
        }

        public string[] GetAllKeys()
        {
            return _cacheItems.Keys.ToArray();
        }

        public string[] GetKeysByPattern(string pattern)
        {
            try
            {
                // Wildcard pattern'i regex'e çevir
                var regexPattern = "^" + Regex.Escape(pattern).Replace("\\*", ".*") + "$";
                var regex = new Regex(regexPattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);

                return _cacheItems.Keys.Where(key => regex.IsMatch(key)).ToArray();
            }
            catch
            {
                // Regex hatası durumunda basit string matching
                return _cacheItems.Keys.Where(key => key.Contains(pattern.Replace("*", ""))).ToArray();
            }
        }

        public CacheStatistics GetStatistics()
        {
            return _statistics;
        }

        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        public bool IsHealthy()
        {
            try
            {
                var testKey = "health_check_" + Guid.NewGuid();
                var testValue = "test";

                Add(testKey, testValue, 1);
                var retrieved = Get<string>(testKey);
                Remove(testKey);

                return retrieved == testValue;
            }
            catch
            {
                return false;
            }
        }

        public Dictionary<string, object> GetHealthInfo()
        {
            return new Dictionary<string, object>
            {
                ["IsHealthy"] = IsHealthy(),
                ["CacheSize"] = GetCacheSize(),
                ["Statistics"] = GetStatistics(),
                ["Configuration"] = _configuration,
                ["MemoryPressure"] = GC.GetTotalMemory(false)
            };
        }

        private void UpdateTagIndex(string key, string[] tags)
        {
            foreach (var tag in tags)
            {
                _tagIndex.AddOrUpdate(tag,
                    new HashSet<string> { key },
                    (_, existingSet) =>
                    {
                        lock (existingSet)
                        {
                            existingSet.Add(key);
                            return existingSet;
                        }
                    });
            }
        }

        private void RemoveFromTagIndex(string key, string[] tags)
        {
            foreach (var tag in tags)
            {
                if (_tagIndex.TryGetValue(tag, out var taggedKeys))
                {
                    lock (taggedKeys)
                    {
                        taggedKeys.Remove(key);
                        if (taggedKeys.Count == 0)
                        {
                            _tagIndex.TryRemove(tag, out _);
                        }
                    }
                }
            }
        }

        public List<int> GetActiveTenants()
        {
            try
            {
                return _cacheItems.Values
                    .Select(item => item.TenantId)
                    .Where(tenantId => tenantId > 0)
                    .Distinct()
                    .OrderBy(id => id)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogDebug($"GetActiveTenants Error: {ex.Message}");
                return new List<int>();
            }
        }

        public Dictionary<string, int> GetEntityCounts(int tenantId)
        {
            try
            {
                return _cacheItems.Values
                    .Where(item => item.TenantId == tenantId)
                    .GroupBy(item => item.EntityName)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                LogDebug($"GetEntityCounts Error: {ex.Message}");
                return new Dictionary<string, int>();
            }
        }

        public List<CacheItemDetail> GetCacheDetails(int tenantId)
        {
            try
            {
                var details = new List<CacheItemDetail>();

                foreach (var item in _cacheItems.Values.Where(i => i.TenantId == tenantId))
                {
                    var detail = new CacheItemDetail
                    {
                        Key = item.Key,
                        TenantId = item.TenantId,
                        EntityName = item.EntityName,
                        Tags = item.Tags,
                        CreatedAt = item.CreatedAt,
                        ExpiresAt = item.ExpiresAt,
                        SizeInBytes = EstimateObjectSize(item.Key),
                        ValueType = GetValueType(item.Key),
                        AccessCount = 0, // Bu bilgiyi tutmak için ek yapı gerekir
                        LastAccessedAt = DateTime.UtcNow
                    };
                    details.Add(detail);
                }

                return details.OrderByDescending(d => d.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                LogDebug($"GetCacheDetails Error: {ex.Message}");
                return new List<CacheItemDetail>();
            }
        }

        public CacheItemDetail GetCacheItemDetail(string key)
        {
            try
            {
                if (_cacheItems.TryGetValue(key, out var item))
                {
                    return new CacheItemDetail
                    {
                        Key = item.Key,
                        TenantId = item.TenantId,
                        EntityName = item.EntityName,
                        Tags = item.Tags,
                        CreatedAt = item.CreatedAt,
                        ExpiresAt = item.ExpiresAt,
                        SizeInBytes = EstimateObjectSize(key),
                        ValueType = GetValueType(key),
                        AccessCount = 0,
                        LastAccessedAt = DateTime.UtcNow
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                LogDebug($"GetCacheItemDetail Error: {ex.Message}");
                return null;
            }
        }

        public long GetTenantCacheSize(int tenantId)
        {
            try
            {
                return _cacheItems.Values
                    .Where(item => item.TenantId == tenantId)
                    .Sum(item => EstimateObjectSize(item.Key));
            }
            catch (Exception ex)
            {
                LogDebug($"GetTenantCacheSize Error: {ex.Message}");
                return 0;
            }
        }

        public Dictionary<int, long> GetAllTenantSizes()
        {
            try
            {
                return _cacheItems.Values
                    .Where(item => item.TenantId > 0)
                    .GroupBy(item => item.TenantId)
                    .ToDictionary(g => g.Key, g => g.Sum(item => EstimateObjectSize(item.Key)));
            }
            catch (Exception ex)
            {
                LogDebug($"GetAllTenantSizes Error: {ex.Message}");
                return new Dictionary<int, long>();
            }
        }

        public Dictionary<string, object> GetPerformanceMetrics()
        {
            try
            {
                var stats = GetStatistics();
                var totalMemory = GC.GetTotalMemory(false);

                // Gerçek cache bellek kullanımını hesapla
                var actualCacheMemoryUsage = CalculateActualCacheMemoryUsage();
                var cacheItemCount = _cacheItems.Count;

                return new Dictionary<string, object>
                {
                    ["TotalMemoryUsage"] = totalMemory,
                    ["CacheMemoryUsage"] = actualCacheMemoryUsage,
                    ["CacheMemoryPercentage"] = totalMemory > 0 ? (double)actualCacheMemoryUsage / totalMemory * 100 : 0,
                    ["TotalKeys"] = cacheItemCount,
                    ["HitRatio"] = stats.HitRatio,
                    ["TotalHits"] = stats.TotalHits,
                    ["TotalMisses"] = stats.TotalMisses,
                    ["ActiveTenants"] = GetActiveTenants().Count,
                    ["AverageKeySize"] = cacheItemCount > 0 ? actualCacheMemoryUsage / cacheItemCount : 0,
                    ["ExpiredKeys"] = _cacheItems.Values.Count(item => DateTime.UtcNow > item.ExpiresAt),
                    ["MemoryPressure"] = GetMemoryPressureLevel(),
                    ["CacheEfficiency"] = CalculateCacheEfficiency(stats),
                    ["TenantDistribution"] = GetTenantMemoryDistribution()
                };
            }
            catch (Exception ex)
            {
                LogDebug($"GetPerformanceMetrics Error: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        private long CalculateActualCacheMemoryUsage()
        {
            try
            {
                long totalSize = 0;
                var sampleSize = Math.Min(_cacheItems.Count, 100); // Performans için örnekleme
                var items = _cacheItems.Take(sampleSize).ToList();

                foreach (var item in items)
                {
                    totalSize += EstimateObjectSize(item.Key);
                }

                // Örnekleme sonucunu tüm cache'e extrapolate et
                if (sampleSize > 0 && _cacheItems.Count > sampleSize)
                {
                    totalSize = (totalSize * _cacheItems.Count) / sampleSize;
                }

                return totalSize;
            }
            catch
            {
                return _cacheItems.Count * 1024; // Fallback: item başına 1KB
            }
        }

        private string GetMemoryPressureLevel()
        {
            try
            {
                var totalMemory = GC.GetTotalMemory(false);
                var cacheMemory = CalculateActualCacheMemoryUsage();
                var percentage = totalMemory > 0 ? (double)cacheMemory / totalMemory * 100 : 0;

                return percentage switch
                {
                    < 5 => "Low",
                    < 15 => "Normal",
                    < 30 => "Medium",
                    < 50 => "High",
                    _ => "Critical"
                };
            }
            catch
            {
                return "Unknown";
            }
        }

        private double CalculateCacheEfficiency(CacheStatistics stats)
        {
            try
            {
                var totalRequests = stats.TotalHits + stats.TotalMisses;
                if (totalRequests == 0) return 0;

                var hitRatio = stats.HitRatio;
                var memoryUsage = CalculateActualCacheMemoryUsage();
                var memoryEfficiency = memoryUsage > 0 ? (double)stats.TotalHits / memoryUsage * 1000000 : 0; // Hits per MB

                return (hitRatio * 0.7) + (Math.Min(memoryEfficiency, 1.0) * 0.3); // Weighted score
            }
            catch
            {
                return 0;
            }
        }

        private Dictionary<int, double> GetTenantMemoryDistribution()
        {
            try
            {
                var tenantSizes = GetAllTenantSizes();
                var totalMemory = tenantSizes.Values.Sum();

                return tenantSizes.ToDictionary(
                    kvp => kvp.Key,
                    kvp => totalMemory > 0 ? (double)kvp.Value / totalMemory * 100 : 0
                );
            }
            catch
            {
                return new Dictionary<int, double>();
            }
        }

        private long EstimateObjectSize(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var value))
                {
                    if (value == null) return 0;

                    // Gelişmiş boyut tahmini - performans odaklı
                    return EstimateObjectSizeRecursive(value);
                }
                return 0;
            }
            catch
            {
                return 100; // Varsayılan boyut
            }
        }

        private long EstimateObjectSizeRecursive(object obj)
        {
            if (obj == null) return 0;

            var type = obj.GetType();

            // Primitive types
            if (type.IsPrimitive)
            {
                return type == typeof(bool) ? 1 :
                       type == typeof(byte) ? 1 :
                       type == typeof(char) ? 2 :
                       type == typeof(short) ? 2 :
                       type == typeof(int) ? 4 :
                       type == typeof(long) ? 8 :
                       type == typeof(float) ? 4 :
                       type == typeof(double) ? 8 : 8;
            }

            // String
            if (type == typeof(string))
            {
                return ((string)obj).Length * 2 + 24; // Unicode + object overhead
            }

            // DateTime
            if (type == typeof(DateTime))
            {
                return 8 + 16; // Ticks + object overhead
            }

            // Collections
            if (obj is System.Collections.IEnumerable enumerable && !(obj is string))
            {
                long size = 32; // Collection overhead
                foreach (var item in enumerable)
                {
                    size += EstimateObjectSizeRecursive(item);
                    if (size > 10000) break; // Performans için limit
                }
                return size;
            }

            // Complex objects - basit tahmin
            var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            long estimatedSize = 24; // Object overhead

            foreach (var prop in properties.Take(10)) // Performans için ilk 10 property
            {
                try
                {
                    var propValue = prop.GetValue(obj);
                    estimatedSize += EstimateObjectSizeRecursive(propValue);
                    if (estimatedSize > 10000) break; // Performans için limit
                }
                catch
                {
                    estimatedSize += 50; // Varsayılan property boyutu
                }
            }

            return estimatedSize;
        }

        private string GetValueType(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var value))
                {
                    return value?.GetType().Name ?? "null";
                }
                return "Unknown";
            }
            catch
            {
                return "Error";
            }
        }

        private async Task<bool> IsTenantIsolatedAsync(int tenantId)
        {
            try
            {
                if (_isolationManager == null)
                {
                    InitializeIsolationManager();
                    if (_isolationManager == null) return false;
                }

                return await _isolationManager.IsTenantIsolatedAsync(tenantId);
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckTenantQuotaAsync(int tenantId, long additionalMemory)
        {
            try
            {
                if (_isolationManager == null)
                {
                    InitializeIsolationManager();
                    if (_isolationManager == null) return true; // Allow if isolation manager not available
                }

                return await _isolationManager.CheckTenantQuotaAsync(tenantId, additionalMemory);
            }
            catch
            {
                return true; // Allow on error
            }
        }

        private long EstimateObjectSize(string key, object value)
        {
            try
            {
                // Key size
                var keySize = System.Text.Encoding.UTF8.GetByteCount(key);

                // Value size estimation
                var valueSize = EstimateObjectSizeRecursive(value);

                return keySize + valueSize + 64; // Add overhead
            }
            catch
            {
                return 1024; // Default 1KB estimate
            }
        }

        private void LogDebug(string message)
        {
            if (_configuration.EnableDebugLogging)
            {
                Debug.WriteLine($"[MultiTenantCache] {DateTime.Now:HH:mm:ss.fff} - {message}");
            }
        }
    }
}
            }
        }
    }

    public class CacheItem
    {
        public string Key { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public string[] Tags { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
    }
}
