using Core.CrossCuttingConcerns.Caching.Isolation.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching.Isolation
{
    public interface ITenantCacheIsolationManager
    {
        // Tenant cache limits
        Task<TenantCacheQuota> GetTenantQuotaAsync(int tenantId);
        Task SetTenantQuotaAsync(int tenantId, TenantCacheQuota quota);
        Task<bool> CheckTenantQuotaAsync(int tenantId, long additionalMemory);
        
        // Memory pressure handling
        Task<MemoryPressureResponse> HandleMemoryPressureAsync(MemoryPressureLevel level);
        Task<List<EvictionCandidate>> GetEvictionCandidatesAsync(int tenantId, long targetMemoryReduction);
        Task<EvictionResult> EvictCacheItemsAsync(List<EvictionCandidate> candidates);
        
        // Tenant isolation
        Task<TenantCacheIsolationStatus> GetTenantIsolationStatusAsync(int tenantId);
        Task<bool> IsTenantIsolatedAsync(int tenantId);
        Task IsolateTenantAsync(int tenantId, string reason);
        Task RestoreTenantAsync(int tenantId);
        
        // Cache allocation strategies
        Task<CacheAllocationStrategy> GetOptimalAllocationStrategyAsync(int tenantId);
        Task ApplyAllocationStrategyAsync(int tenantId, CacheAllocationStrategy strategy);
        
        // Monitoring and alerts
        Task<List<TenantCacheAlert>> GetActiveAlertsAsync();
        Task<TenantCacheAlert> CreateAlertAsync(int tenantId, AlertType alertType, string message, AlertSeverity severity);
        Task ResolveAlertAsync(string alertId);
        
        // Auto-scaling
        Task<AutoScalingRecommendation> GetAutoScalingRecommendationAsync(int tenantId);
        Task<bool> CanAutoScaleAsync(int tenantId);
        Task<AutoScalingResult> AutoScaleTenantCacheAsync(int tenantId);
        
        // Performance optimization
        Task<TenantPerformanceProfile> GetTenantPerformanceProfileAsync(int tenantId);
        Task OptimizeTenantCacheAsync(int tenantId);
        
        // Bulk operations
        Task<Dictionary<int, TenantCacheQuota>> GetAllTenantQuotasAsync();
        Task<BulkOperationResult> ApplyBulkQuotaChangesAsync(Dictionary<int, TenantCacheQuota> quotaChanges);
        Task<GlobalCacheOptimizationResult> OptimizeGlobalCacheAllocationAsync();
        
        // Emergency operations
        Task<EmergencyResponse> HandleEmergencyMemoryPressureAsync();
        Task<bool> ForceEvictTenantCacheAsync(int tenantId, double percentage);
        Task<SystemRecoveryResult> RecoverFromMemoryExhaustionAsync();
    }
}
