using Core.CrossCuttingConcerns.Caching.Analytics.Models;
using Core.CrossCuttingConcerns.Caching.Configuration;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching.Analytics
{
    public class CacheAnalyticsService : ICacheAnalyticsService
    {
        private readonly ICacheManager _cacheManager;
        private readonly CacheConfiguration _configuration;
        
        // In-memory storage for analytics data (production'da database kullanılmalı)
        private readonly ConcurrentQueue<CacheOperation> _operationHistory;
        private readonly ConcurrentQueue<CacheMetricsHistory> _metricsHistory;
        private readonly object _lockObject = new object();

        public CacheAnalyticsService(ICacheManager cacheManager, CacheConfiguration configuration)
        {
            _cacheManager = cacheManager;
            _configuration = configuration;
            _operationHistory = new ConcurrentQueue<CacheOperation>();
            _metricsHistory = new ConcurrentQueue<CacheMetricsHistory>();
        }

        public async Task<CacheAnalyticsSnapshot> GetCurrentSnapshotAsync()
        {
            return await Task.Run(() =>
            {
                var stats = _cacheManager.GetStatistics();
                var performanceMetrics = _cacheManager.GetPerformanceMetrics();
                var activeTenants = _cacheManager.GetActiveTenants();

                var snapshot = new CacheAnalyticsSnapshot
                {
                    Timestamp = DateTime.UtcNow,
                    TotalMemoryUsage = (long)performanceMetrics["TotalMemoryUsage"],
                    CacheMemoryUsage = (long)performanceMetrics["CacheMemoryUsage"],
                    MemoryPressureLevel = (double)performanceMetrics["CacheMemoryPercentage"],
                    TotalKeys = (int)performanceMetrics["TotalKeys"],
                    ActiveTenants = activeTenants.Count,
                    OverallHitRatio = stats.HitRatio
                };

                // Tenant snapshots
                foreach (var tenantId in activeTenants)
                {
                    var tenantSize = _cacheManager.GetTenantCacheSize(tenantId);
                    var entityCounts = _cacheManager.GetEntityCounts(tenantId);
                    
                    snapshot.TenantSnapshots[tenantId] = new TenantSnapshot
                    {
                        TenantId = tenantId,
                        MemoryUsage = tenantSize,
                        KeyCount = entityCounts.Values.Sum(),
                        HitRatio = CalculateTenantHitRatio(tenantId),
                        LastActivity = DateTime.UtcNow,
                        EntityCounts = entityCounts
                    };
                }

                // Entity snapshots
                var allEntityNames = GetAllEntityNames();
                foreach (var entityName in allEntityNames)
                {
                    snapshot.EntitySnapshots[entityName] = CalculateEntitySnapshot(entityName);
                }

                return snapshot;
            });
        }

        public async Task<List<CacheUsagePattern>> GetUsagePatternsAsync(int tenantId, TimeSpan timeRange)
        {
            return await Task.Run(() =>
            {
                var cutoffTime = DateTime.UtcNow - timeRange;
                var relevantOperations = _operationHistory
                    .Where(op => op.TenantId == tenantId && op.Timestamp >= cutoffTime)
                    .ToList();

                return relevantOperations
                    .GroupBy(op => new { op.EntityName, op.OperationType, Hour = op.Timestamp.Hour })
                    .Select(group => new CacheUsagePattern
                    {
                        TenantId = tenantId,
                        EntityName = group.Key.EntityName,
                        OperationType = group.Key.OperationType,
                        TimeWindow = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.UtcNow.Day, group.Key.Hour, 0, 0),
                        OperationCount = group.Count(),
                        HitRatio = group.Count(op => op.IsHit) / (double)group.Count(),
                        AverageResponseTime = (long)group.Average(op => op.ResponseTimeMs),
                        MemoryImpact = group.Sum(op => op.MemoryImpact)
                    })
                    .OrderByDescending(p => p.OperationCount)
                    .ToList();
            });
        }

        public async Task<MemoryUsageAnalysis> GetMemoryUsageAnalysisAsync()
        {
            return await Task.Run(() =>
            {
                var performanceMetrics = _cacheManager.GetPerformanceMetrics();
                var totalMemory = (long)performanceMetrics["TotalMemoryUsage"];
                var cacheMemory = (long)performanceMetrics["CacheMemoryUsage"];
                var memoryPercentage = (double)performanceMetrics["CacheMemoryPercentage"];

                var analysis = new MemoryUsageAnalysis
                {
                    AnalysisTime = DateTime.UtcNow,
                    TotalSystemMemory = totalMemory,
                    CacheMemoryUsage = cacheMemory,
                    MemoryPressurePercentage = memoryPercentage,
                    PressureLevel = GetMemoryPressureLevel(memoryPercentage),
                    Hotspots = IdentifyMemoryHotspots(),
                    Trend = CalculateMemoryTrend(),
                    EstimatedOptimalSize = EstimateOptimalCacheSize()
                };

                return analysis;
            });
        }

        public async Task<List<CacheMetricsHistory>> GetHistoricalDataAsync(DateTime startDate, DateTime endDate)
        {
            return await Task.Run(() =>
            {
                return _metricsHistory
                    .Where(h => h.Timestamp >= startDate && h.Timestamp <= endDate)
                    .OrderBy(h => h.Timestamp)
                    .ToList();
            });
        }

        public async Task<TenantCacheAnalysis> GetTenantAnalysisAsync(int tenantId, TimeSpan timeRange)
        {
            return await Task.Run(() =>
            {
                var cutoffTime = DateTime.UtcNow - timeRange;
                var tenantOperations = _operationHistory
                    .Where(op => op.TenantId == tenantId && op.Timestamp >= cutoffTime)
                    .ToList();

                var entityStats = tenantOperations
                    .GroupBy(op => op.EntityName)
                    .ToDictionary(g => g.Key, g => new EntityUsageStats
                    {
                        EntityName = g.Key,
                        MemoryUsage = g.Sum(op => op.MemoryImpact),
                        KeyCount = g.Select(op => op.CacheKey).Distinct().Count(),
                        HitRatio = g.Count(op => op.IsHit) / (double)g.Count(),
                        AccessCount = g.Count(),
                        AverageDuration = TimeSpan.FromMilliseconds(g.Average(op => op.ResponseTimeMs)),
                        EfficiencyScore = CalculateEntityEfficiency(g.ToList())
                    });

                var analysis = new TenantCacheAnalysis
                {
                    TenantId = tenantId,
                    AnalysisPeriod = timeRange,
                    AverageMemoryUsage = (long)tenantOperations.Average(op => op.MemoryImpact),
                    PeakMemoryUsage = tenantOperations.Max(op => op.MemoryImpact),
                    AverageHitRatio = tenantOperations.Count(op => op.IsHit) / (double)tenantOperations.Count,
                    TotalOperations = tenantOperations.Count,
                    EntityStats = entityStats,
                    UsagePatterns = await GetUsagePatternsAsync(tenantId, timeRange),
                    HealthScore = CalculateTenantHealthScore(tenantId, entityStats)
                };

                return analysis;
            });
        }

        public async Task<List<CacheHotspot>> GetCacheHotspotsAsync()
        {
            return await Task.Run(() =>
            {
                var hotspots = new List<CacheHotspot>();
                var tenantSizes = _cacheManager.GetAllTenantSizes();
                var totalMemory = tenantSizes.Values.Sum();

                // Memory usage hotspots
                foreach (var tenant in tenantSizes.Where(t => t.Value > totalMemory * 0.1)) // 10%'den fazla kullananlar
                {
                    hotspots.Add(new CacheHotspot
                    {
                        Identifier = $"HighMemory_T{tenant.Key}",
                        Type = HotspotType.HighMemoryUsage,
                        TenantId = tenant.Key,
                        MemoryUsage = tenant.Value,
                        ImpactScore = (double)tenant.Value / totalMemory * 100,
                        Description = $"Tenant {tenant.Key} toplam cache belleğinin %{(double)tenant.Value / totalMemory * 100:F1}'ini kullanıyor",
                        Recommendations = new List<string>
                        {
                            "Cache sürelerini gözden geçirin",
                            "Gereksiz cache'leri temizleyin",
                            "Entity bazlı optimizasyon yapın"
                        }
                    });
                }

                // Low hit ratio hotspots
                var activeTenants = _cacheManager.GetActiveTenants();
                foreach (var tenantId in activeTenants)
                {
                    var hitRatio = CalculateTenantHitRatio(tenantId);
                    if (hitRatio < 0.5) // %50'den düşük hit ratio
                    {
                        hotspots.Add(new CacheHotspot
                        {
                            Identifier = $"LowHitRatio_T{tenantId}",
                            Type = HotspotType.LowHitRatio,
                            TenantId = tenantId,
                            ImpactScore = (1 - hitRatio) * 100,
                            Description = $"Tenant {tenantId} düşük cache hit oranına sahip (%{hitRatio * 100:F1})",
                            Recommendations = new List<string>
                            {
                                "Cache stratejisini gözden geçirin",
                                "Cache sürelerini artırın",
                                "Veri erişim desenlerini analiz edin"
                            }
                        });
                    }
                }

                return hotspots.OrderByDescending(h => h.ImpactScore).ToList();
            });
        }

        public async Task<List<CacheInefficiency>> GetInefficienciesAsync()
        {
            return await Task.Run(() =>
            {
                var inefficiencies = new List<CacheInefficiency>();
                
                // Analyze each entity configuration
                foreach (var entityConfig in _configuration.EntitySettings)
                {
                    var entityName = entityConfig.Key;
                    var config = entityConfig.Value;
                    
                    // Check for over-caching (long duration with low hit ratio)
                    var entitySnapshot = CalculateEntitySnapshot(entityName);
                    if (config.DefaultDuration > 60 && entitySnapshot.AverageHitRatio < 0.3)
                    {
                        inefficiencies.Add(new CacheInefficiency
                        {
                            Type = "OverCaching",
                            EntityName = entityName,
                            Description = $"{entityName} entity'si uzun süre cache'leniyor ama düşük hit ratio'ya sahip",
                            ImpactScore = (config.DefaultDuration / 60.0) * (1 - entitySnapshot.AverageHitRatio) * 100,
                            WastedMemory = (long)(entitySnapshot.TotalMemoryUsage * (1 - entitySnapshot.AverageHitRatio)),
                            Recommendation = "Cache süresini azaltın veya cache stratejisini değiştirin",
                            PotentialSavings = entitySnapshot.TotalMemoryUsage * 0.3
                        });
                    }
                    
                    // Check for under-caching (short duration with high hit ratio)
                    if (config.DefaultDuration < 30 && entitySnapshot.AverageHitRatio > 0.8)
                    {
                        inefficiencies.Add(new CacheInefficiency
                        {
                            Type = "UnderCaching",
                            EntityName = entityName,
                            Description = $"{entityName} entity'si kısa süre cache'leniyor ama yüksek hit ratio'ya sahip",
                            ImpactScore = entitySnapshot.AverageHitRatio * (30 - config.DefaultDuration) * 10,
                            Recommendation = "Cache süresini artırın",
                            PotentialSavings = 0 // Performance gain, not memory saving
                        });
                    }
                }

                return inefficiencies.OrderByDescending(i => i.ImpactScore).ToList();
            });
        }

        public async Task<CachePrediction> PredictMemoryUsageAsync(TimeSpan forecastPeriod)
        {
            return await Task.Run(() =>
            {
                var recentHistory = _metricsHistory
                    .Where(h => h.Timestamp >= DateTime.UtcNow.AddDays(-7))
                    .OrderBy(h => h.Timestamp)
                    .ToList();

                if (recentHistory.Count < 2)
                {
                    return new CachePrediction
                    {
                        PredictionTime = DateTime.UtcNow,
                        ForecastPeriod = forecastPeriod,
                        ConfidenceLevel = 0,
                        Assumptions = new List<string> { "Yetersiz veri - tahmin yapılamıyor" }
                    };
                }

                // Simple linear regression for memory usage
                var growthRate = CalculateGrowthRate(recentHistory);
                var currentMemory = recentHistory.Last().MemoryUsage;
                var forecastHours = forecastPeriod.TotalHours;
                
                var prediction = new CachePrediction
                {
                    PredictionTime = DateTime.UtcNow,
                    ForecastPeriod = forecastPeriod,
                    PredictedMemoryUsage = (long)(currentMemory * Math.Pow(1 + growthRate, forecastHours)),
                    PredictedKeyCount = (int)(recentHistory.Last().KeyCount * Math.Pow(1 + growthRate, forecastHours)),
                    ConfidenceLevel = CalculateConfidenceLevel(recentHistory),
                    Assumptions = new List<string>
                    {
                        "Mevcut kullanım desenlerinin devam edeceği",
                        "Yeni tenant'ların eklenmeyeceği",
                        "Cache konfigürasyonunun değişmeyeceği"
                    }
                };

                // Tenant-specific predictions
                var activeTenants = _cacheManager.GetActiveTenants();
                foreach (var tenantId in activeTenants)
                {
                    var tenantGrowthRate = CalculateTenantGrowthRate(tenantId);
                    var currentTenantMemory = _cacheManager.GetTenantCacheSize(tenantId);
                    
                    prediction.TenantPredictions.Add(new TenantPrediction
                    {
                        TenantId = tenantId,
                        PredictedMemoryUsage = (long)(currentTenantMemory * Math.Pow(1 + tenantGrowthRate, forecastHours)),
                        GrowthRate = tenantGrowthRate,
                        RiskLevel = DetermineRiskLevel(tenantGrowthRate)
                    });
                }

                return prediction;
            });
        }

        // Helper methods will be implemented in the next part...
        private double CalculateTenantHitRatio(int tenantId)
        {
            var recentOps = _operationHistory
                .Where(op => op.TenantId == tenantId && op.Timestamp >= DateTime.UtcNow.AddHours(-1))
                .ToList();
            
            if (!recentOps.Any()) return 0;
            return recentOps.Count(op => op.IsHit) / (double)recentOps.Count;
        }

        private List<string> GetAllEntityNames()
        {
            return _configuration.EntitySettings.Keys.ToList();
        }

        private EntitySnapshot CalculateEntitySnapshot(string entityName)
        {
            var activeTenants = _cacheManager.GetActiveTenants();
            var totalMemory = 0L;
            var totalKeys = 0;
            var hitRatios = new List<double>();
            var tenantsUsing = 0;

            foreach (var tenantId in activeTenants)
            {
                var entityCounts = _cacheManager.GetEntityCounts(tenantId);
                if (entityCounts.ContainsKey(entityName))
                {
                    tenantsUsing++;
                    totalKeys += entityCounts[entityName];
                    // Memory calculation would need more detailed implementation
                    totalMemory += entityCounts[entityName] * 1024; // Rough estimate
                    hitRatios.Add(CalculateTenantHitRatio(tenantId));
                }
            }

            return new EntitySnapshot
            {
                EntityName = entityName,
                TotalMemoryUsage = totalMemory,
                TotalKeys = totalKeys,
                AverageHitRatio = hitRatios.Any() ? hitRatios.Average() : 0,
                AverageDuration = TimeSpan.FromMinutes(_configuration.EntitySettings.ContainsKey(entityName) 
                    ? _configuration.EntitySettings[entityName].DefaultDuration 
                    : _configuration.DefaultDurationMinutes),
                TenantsUsingEntity = tenantsUsing
            };
        }

        private string GetMemoryPressureLevel(double percentage)
        {
            return percentage switch
            {
                < 5 => "Low",
                < 15 => "Normal",
                < 30 => "Medium", 
                < 50 => "High",
                _ => "Critical"
            };
        }

        private List<MemoryHotspot> IdentifyMemoryHotspots()
        {
            // Implementation for identifying memory hotspots
            return new List<MemoryHotspot>();
        }

        private MemoryTrend CalculateMemoryTrend()
        {
            // Implementation for calculating memory trends
            return new MemoryTrend { Direction = TrendDirection.Stable };
        }

        private long EstimateOptimalCacheSize()
        {
            // Implementation for estimating optimal cache size
            return _cacheManager.GetCacheSize();
        }

        private double CalculateEntityEfficiency(List<CacheOperation> operations)
        {
            if (!operations.Any()) return 0;
            
            var hitRatio = operations.Count(op => op.IsHit) / (double)operations.Count;
            var avgResponseTime = operations.Average(op => op.ResponseTimeMs);
            var memoryEfficiency = operations.Sum(op => op.MemoryImpact) / (double)operations.Count;
            
            // Weighted efficiency score
            return (hitRatio * 0.5) + ((1000 - Math.Min(avgResponseTime, 1000)) / 1000 * 0.3) + 
                   ((10000 - Math.Min(memoryEfficiency, 10000)) / 10000 * 0.2);
        }

        private TenantHealthScore CalculateTenantHealthScore(int tenantId, Dictionary<string, EntityUsageStats> entityStats)
        {
            var memoryEfficiency = entityStats.Values.Average(e => e.EfficiencyScore);
            var hitRatio = entityStats.Values.Average(e => e.HitRatio);
            var configScore = CalculateConfigurationScore(entityStats);
            
            return new TenantHealthScore
            {
                OverallScore = (memoryEfficiency + hitRatio + configScore) / 3,
                MemoryEfficiency = memoryEfficiency,
                CacheHitRatio = hitRatio,
                ConfigurationOptimization = configScore
            };
        }

        private double CalculateConfigurationScore(Dictionary<string, EntityUsageStats> entityStats)
        {
            // Implementation for configuration scoring
            return 0.8; // Placeholder
        }

        private double CalculateGrowthRate(List<CacheMetricsHistory> history)
        {
            if (history.Count < 2) return 0;
            
            var first = history.First();
            var last = history.Last();
            var timeDiff = (last.Timestamp - first.Timestamp).TotalHours;
            
            if (timeDiff == 0) return 0;
            
            return (double)(last.MemoryUsage - first.MemoryUsage) / first.MemoryUsage / timeDiff;
        }

        private double CalculateConfidenceLevel(List<CacheMetricsHistory> history)
        {
            // Simple confidence calculation based on data consistency
            if (history.Count < 5) return 0.3;
            if (history.Count < 20) return 0.6;
            return 0.8;
        }

        private double CalculateTenantGrowthRate(int tenantId)
        {
            // Implementation for tenant-specific growth rate
            return 0.01; // Placeholder: 1% growth per hour
        }

        private string DetermineRiskLevel(double growthRate)
        {
            return growthRate switch
            {
                < 0.01 => "Low",
                < 0.05 => "Medium",
                _ => "High"
            };
        }

        // Data collection methods
        public async Task RecordCacheOperationAsync(CacheOperation operation)
        {
            await Task.Run(() =>
            {
                _operationHistory.Enqueue(operation);
                
                // Keep only recent operations (last 24 hours)
                while (_operationHistory.TryPeek(out var oldest) && 
                       oldest.Timestamp < DateTime.UtcNow.AddDays(-1))
                {
                    _operationHistory.TryDequeue(out _);
                }
            });
        }

        public async Task RecordMemorySnapshotAsync()
        {
            await Task.Run(() =>
            {
                var performanceMetrics = _cacheManager.GetPerformanceMetrics();
                var stats = _cacheManager.GetStatistics();
                
                var snapshot = new CacheMetricsHistory
                {
                    Timestamp = DateTime.UtcNow,
                    MemoryUsage = (long)performanceMetrics["CacheMemoryUsage"],
                    KeyCount = (int)performanceMetrics["TotalKeys"],
                    HitRatio = stats.HitRatio,
                    ActiveTenants = (int)performanceMetrics["ActiveTenants"],
                    MemoryPressure = (double)performanceMetrics["CacheMemoryPercentage"]
                };
                
                _metricsHistory.Enqueue(snapshot);
                
                // Keep only recent snapshots (last 7 days)
                while (_metricsHistory.TryPeek(out var oldest) && 
                       oldest.Timestamp < DateTime.UtcNow.AddDays(-7))
                {
                    _metricsHistory.TryDequeue(out _);
                }
            });
        }

        public async Task CleanupOldDataAsync(TimeSpan retentionPeriod)
        {
            await Task.Run(() =>
            {
                var cutoffTime = DateTime.UtcNow - retentionPeriod;
                
                // Cleanup operations
                while (_operationHistory.TryPeek(out var oldOp) && oldOp.Timestamp < cutoffTime)
                {
                    _operationHistory.TryDequeue(out _);
                }
                
                // Cleanup metrics
                while (_metricsHistory.TryPeek(out var oldMetric) && oldMetric.Timestamp < cutoffTime)
                {
                    _metricsHistory.TryDequeue(out _);
                }
            });
        }

        public async Task<List<CacheOptimizationRecommendation>> GetOptimizationRecommendationsAsync()
        {
            return await Task.Run(() =>
            {
                var recommendations = new List<CacheOptimizationRecommendation>();
                var inefficiencies = GetInefficienciesAsync().Result;

                foreach (var inefficiency in inefficiencies.Take(5)) // Top 5 recommendations
                {
                    recommendations.Add(new CacheOptimizationRecommendation
                    {
                        Id = Guid.NewGuid().ToString(),
                        Type = inefficiency.Type == "OverCaching" ? OptimizationType.DurationAdjustment : OptimizationType.ConfigurationChange,
                        Title = $"Optimize {inefficiency.EntityName} Cache Configuration",
                        Description = inefficiency.Description,
                        Priority = (int)inefficiency.ImpactScore / 10,
                        ImpactScore = inefficiency.ImpactScore,
                        EstimatedMemorySavings = inefficiency.WastedMemory,
                        EstimatedPerformanceGain = inefficiency.PotentialSavings,
                        Implementation = inefficiency.Recommendation,
                        Parameters = new Dictionary<string, object>
                        {
                            ["EntityName"] = inefficiency.EntityName,
                            ["CurrentIssue"] = inefficiency.Type
                        }
                    });
                }

                return recommendations.OrderByDescending(r => r.ImpactScore).ToList();
            });
        }

        public async Task<CacheHealthScore> GetCacheHealthScoreAsync()
        {
            return await Task.Run(() =>
            {
                var performanceMetrics = _cacheManager.GetPerformanceMetrics();
                var stats = _cacheManager.GetStatistics();

                var memoryEfficiency = CalculateMemoryEfficiencyScore(performanceMetrics);
                var hitRatioScore = stats.HitRatio;
                var configScore = CalculateOverallConfigurationScore();
                var performanceScore = CalculatePerformanceScore(performanceMetrics);
                var stabilityScore = CalculateStabilityScore();

                var overallScore = (memoryEfficiency + hitRatioScore + configScore + performanceScore + stabilityScore) / 5;

                return new CacheHealthScore
                {
                    OverallScore = overallScore,
                    CalculatedAt = DateTime.UtcNow,
                    Breakdown = new HealthScoreBreakdown
                    {
                        MemoryEfficiency = memoryEfficiency,
                        HitRatioScore = hitRatioScore,
                        ConfigurationScore = configScore,
                        PerformanceScore = performanceScore,
                        StabilityScore = stabilityScore
                    },
                    HealthLevel = DetermineHealthLevel(overallScore),
                    Issues = IdentifyHealthIssues(overallScore, memoryEfficiency, hitRatioScore),
                    Recommendations = GenerateHealthRecommendations(memoryEfficiency, hitRatioScore, configScore)
                };
            });
        }

        public async Task<CacheConfigurationAnalysis> AnalyzeCacheConfigurationAsync()
        {
            return await Task.Run(() =>
            {
                var analysis = new CacheConfigurationAnalysis
                {
                    AnalysisTime = DateTime.UtcNow,
                    Issues = new List<ConfigurationIssue>(),
                    Recommendations = new List<ConfigurationRecommendation>(),
                    EntityAnalysis = new Dictionary<string, EntityConfigAnalysis>()
                };

                foreach (var entityConfig in _configuration.EntitySettings)
                {
                    var entityName = entityConfig.Key;
                    var config = entityConfig.Value;
                    var entitySnapshot = CalculateEntitySnapshot(entityName);

                    var entityAnalysis = new EntityConfigAnalysis
                    {
                        EntityName = entityName,
                        CurrentDuration = config.DefaultDuration,
                        RecommendedDuration = CalculateOptimalDuration(entitySnapshot),
                        MemoryEfficiency = CalculateEntityMemoryEfficiency(entitySnapshot),
                        HitRatio = entitySnapshot.AverageHitRatio,
                        CurrentTags = config.Tags,
                        RecommendedTags = GenerateOptimalTags(entityName, entitySnapshot),
                        IsOptimal = IsConfigurationOptimal(config, entitySnapshot)
                    };

                    analysis.EntityAnalysis[entityName] = entityAnalysis;

                    // Generate issues and recommendations
                    if (!entityAnalysis.IsOptimal)
                    {
                        if (entityAnalysis.CurrentDuration != entityAnalysis.RecommendedDuration)
                        {
                            analysis.Issues.Add(new ConfigurationIssue
                            {
                                EntityName = entityName,
                                IssueType = "SuboptimalDuration",
                                Description = $"Cache duration ({config.DefaultDuration}min) is not optimal for hit ratio ({entitySnapshot.AverageHitRatio:P})",
                                Severity = entitySnapshot.AverageHitRatio < 0.5 ? "High" : "Medium",
                                Impact = Math.Abs(entityAnalysis.CurrentDuration - entityAnalysis.RecommendedDuration) * entitySnapshot.TotalMemoryUsage / 1024
                            });

                            analysis.Recommendations.Add(new ConfigurationRecommendation
                            {
                                EntityName = entityName,
                                Parameter = "DefaultDuration",
                                CurrentValue = config.DefaultDuration,
                                RecommendedValue = entityAnalysis.RecommendedDuration,
                                Reason = $"Optimize for {entitySnapshot.AverageHitRatio:P} hit ratio",
                                ExpectedImprovement = CalculateExpectedImprovement(entityAnalysis)
                            });
                        }
                    }
                }

                analysis.OptimizationScore = CalculateOptimizationScore(analysis.EntityAnalysis.Values);
                return analysis;
            });
        }

        public async Task<List<EntityCacheAnalysis>> GetEntityAnalysisAsync()
        {
            return await Task.Run(() =>
            {
                var entityAnalyses = new List<EntityCacheAnalysis>();

                foreach (var entityName in GetAllEntityNames())
                {
                    var entitySnapshot = CalculateEntitySnapshot(entityName);
                    var activeTenants = _cacheManager.GetActiveTenants();
                    var tenantUsages = new List<TenantEntityUsage>();

                    foreach (var tenantId in activeTenants)
                    {
                        var entityCounts = _cacheManager.GetEntityCounts(tenantId);
                        if (entityCounts.ContainsKey(entityName))
                        {
                            tenantUsages.Add(new TenantEntityUsage
                            {
                                TenantId = tenantId,
                                MemoryUsage = entityCounts[entityName] * 1024, // Rough estimate
                                KeyCount = entityCounts[entityName],
                                HitRatio = CalculateTenantHitRatio(tenantId),
                                AccessCount = GetTenantEntityAccessCount(tenantId, entityName)
                            });
                        }
                    }

                    var analysis = new EntityCacheAnalysis
                    {
                        EntityName = entityName,
                        TotalMemoryUsage = entitySnapshot.TotalMemoryUsage,
                        TotalKeys = entitySnapshot.TotalKeys,
                        AverageHitRatio = entitySnapshot.AverageHitRatio,
                        TenantsUsing = entitySnapshot.TenantsUsingEntity,
                        AverageDuration = entitySnapshot.AverageDuration,
                        EfficiencyScore = CalculateEntityOverallEfficiency(entitySnapshot, tenantUsages),
                        TenantUsages = tenantUsages,
                        OptimizationPotential = CalculateEntityOptimizationPotential(entitySnapshot, tenantUsages)
                    };

                    entityAnalyses.Add(analysis);
                }

                return entityAnalyses.OrderByDescending(e => e.OptimizationPotential.MemorySavingsPotential).ToList();
            });
        }

        // Additional helper methods
        private double CalculateMemoryEfficiencyScore(Dictionary<string, object> performanceMetrics)
        {
            var memoryPercentage = (double)performanceMetrics["CacheMemoryPercentage"];
            var hitRatio = (double)performanceMetrics["HitRatio"];

            // Good efficiency: low memory usage with high hit ratio
            return Math.Max(0, 1 - (memoryPercentage / 100)) * hitRatio;
        }

        private double CalculateOverallConfigurationScore()
        {
            var totalEntities = _configuration.EntitySettings.Count;
            var optimalEntities = 0;

            foreach (var entityConfig in _configuration.EntitySettings)
            {
                var entitySnapshot = CalculateEntitySnapshot(entityConfig.Key);
                if (IsConfigurationOptimal(entityConfig.Value, entitySnapshot))
                {
                    optimalEntities++;
                }
            }

            return totalEntities > 0 ? (double)optimalEntities / totalEntities : 1.0;
        }

        private double CalculatePerformanceScore(Dictionary<string, object> performanceMetrics)
        {
            var hitRatio = (double)performanceMetrics["HitRatio"];
            var expiredKeys = (int)performanceMetrics["ExpiredKeys"];
            var totalKeys = (int)performanceMetrics["TotalKeys"];

            var expiredRatio = totalKeys > 0 ? (double)expiredKeys / totalKeys : 0;
            return (hitRatio * 0.7) + ((1 - expiredRatio) * 0.3);
        }

        private double CalculateStabilityScore()
        {
            // Based on recent memory usage variance
            var recentMetrics = _metricsHistory
                .Where(m => m.Timestamp >= DateTime.UtcNow.AddHours(-2))
                .ToList();

            if (recentMetrics.Count < 2) return 0.8; // Default stable score

            var memoryValues = recentMetrics.Select(m => (double)m.MemoryUsage).ToList();
            var variance = CalculateVariance(memoryValues);
            var mean = memoryValues.Average();
            var coefficientOfVariation = mean > 0 ? Math.Sqrt(variance) / mean : 0;

            return Math.Max(0, 1 - coefficientOfVariation);
        }

        private double CalculateVariance(List<double> values)
        {
            if (values.Count < 2) return 0;

            var mean = values.Average();
            return values.Sum(v => Math.Pow(v - mean, 2)) / values.Count;
        }

        private string DetermineHealthLevel(double overallScore)
        {
            return overallScore switch
            {
                >= 0.9 => "Excellent",
                >= 0.8 => "Good",
                >= 0.7 => "Fair",
                >= 0.6 => "Poor",
                _ => "Critical"
            };
        }

        private List<HealthIssue> IdentifyHealthIssues(double overallScore, double memoryEfficiency, double hitRatio)
        {
            var issues = new List<HealthIssue>();

            if (memoryEfficiency < 0.6)
            {
                issues.Add(new HealthIssue
                {
                    Type = "MemoryInefficiency",
                    Description = "Cache memory efficiency is below optimal levels",
                    Severity = memoryEfficiency < 0.4 ? "High" : "Medium",
                    Impact = (0.6 - memoryEfficiency) * 100,
                    Recommendation = "Review cache durations and remove unnecessary cached data"
                });
            }

            if (hitRatio < 0.7)
            {
                issues.Add(new HealthIssue
                {
                    Type = "LowHitRatio",
                    Description = "Cache hit ratio is below recommended levels",
                    Severity = hitRatio < 0.5 ? "High" : "Medium",
                    Impact = (0.7 - hitRatio) * 100,
                    Recommendation = "Analyze access patterns and adjust cache strategies"
                });
            }

            return issues;
        }

        private List<HealthRecommendation> GenerateHealthRecommendations(double memoryEfficiency, double hitRatio, double configScore)
        {
            var recommendations = new List<HealthRecommendation>();

            if (memoryEfficiency < 0.8)
            {
                recommendations.Add(new HealthRecommendation
                {
                    Action = "Optimize memory usage",
                    Reason = "Current memory efficiency can be improved",
                    Priority = memoryEfficiency < 0.6 ? 1 : 2,
                    ExpectedImprovement = (0.8 - memoryEfficiency) * 100
                });
            }

            if (hitRatio < 0.8)
            {
                recommendations.Add(new HealthRecommendation
                {
                    Action = "Improve cache hit ratio",
                    Reason = "Cache effectiveness can be enhanced",
                    Priority = hitRatio < 0.6 ? 1 : 2,
                    ExpectedImprovement = (0.8 - hitRatio) * 100
                });
            }

            return recommendations.OrderBy(r => r.Priority).ToList();
        }

        private int CalculateOptimalDuration(EntitySnapshot entitySnapshot)
        {
            // Simple heuristic: adjust duration based on hit ratio
            var baseDuration = _configuration.EntitySettings.ContainsKey(entitySnapshot.EntityName)
                ? _configuration.EntitySettings[entitySnapshot.EntityName].DefaultDuration
                : _configuration.DefaultDurationMinutes;

            if (entitySnapshot.AverageHitRatio > 0.8) return Math.Min(baseDuration * 2, 240); // Max 4 hours
            if (entitySnapshot.AverageHitRatio < 0.5) return Math.Max(baseDuration / 2, 5);   // Min 5 minutes

            return baseDuration;
        }

        private double CalculateEntityMemoryEfficiency(EntitySnapshot entitySnapshot)
        {
            if (entitySnapshot.TotalMemoryUsage == 0) return 1.0;

            var memoryPerKey = entitySnapshot.TotalKeys > 0 ? entitySnapshot.TotalMemoryUsage / entitySnapshot.TotalKeys : 0;
            var hitRatio = entitySnapshot.AverageHitRatio;

            // Efficiency = hit ratio weighted by memory usage per key
            return hitRatio * Math.Max(0, 1 - (memoryPerKey / 10240)); // Penalize if > 10KB per key
        }

        private string[] GenerateOptimalTags(string entityName, EntitySnapshot entitySnapshot)
        {
            var tags = new List<string> { entityName };

            if (entitySnapshot.AverageHitRatio > 0.8) tags.Add("HighPerformance");
            if (entitySnapshot.TotalMemoryUsage > 1024 * 1024) tags.Add("LargeData"); // > 1MB
            if (entitySnapshot.TenantsUsingEntity > 10) tags.Add("Popular");

            return tags.ToArray();
        }

        private bool IsConfigurationOptimal(EntityCacheSettings config, EntitySnapshot entitySnapshot)
        {
            var optimalDuration = CalculateOptimalDuration(entitySnapshot);
            return Math.Abs(config.DefaultDuration - optimalDuration) <= 5; // Within 5 minutes tolerance
        }

        private double CalculateExpectedImprovement(EntityConfigAnalysis entityAnalysis)
        {
            var durationDiff = Math.Abs(entityAnalysis.CurrentDuration - entityAnalysis.RecommendedDuration);
            return Math.Min(durationDiff / (double)entityAnalysis.CurrentDuration * 100, 50); // Max 50% improvement
        }

        private double CalculateOptimizationScore(IEnumerable<EntityConfigAnalysis> entityAnalyses)
        {
            var analyses = entityAnalyses.ToList();
            if (!analyses.Any()) return 1.0;

            return analyses.Count(a => a.IsOptimal) / (double)analyses.Count;
        }

        private int GetTenantEntityAccessCount(int tenantId, string entityName)
        {
            return _operationHistory
                .Count(op => op.TenantId == tenantId &&
                           op.EntityName == entityName &&
                           op.Timestamp >= DateTime.UtcNow.AddHours(-1));
        }

        private double CalculateEntityOverallEfficiency(EntitySnapshot entitySnapshot, List<TenantEntityUsage> tenantUsages)
        {
            if (!tenantUsages.Any()) return 0;

            var avgHitRatio = tenantUsages.Average(t => t.HitRatio);
            var memoryEfficiency = CalculateEntityMemoryEfficiency(entitySnapshot);
            var usageDistribution = CalculateUsageDistribution(tenantUsages);

            return (avgHitRatio * 0.4) + (memoryEfficiency * 0.4) + (usageDistribution * 0.2);
        }

        private double CalculateUsageDistribution(List<TenantEntityUsage> tenantUsages)
        {
            if (tenantUsages.Count <= 1) return 1.0;

            var memoryValues = tenantUsages.Select(t => (double)t.MemoryUsage).ToList();
            var variance = CalculateVariance(memoryValues);
            var mean = memoryValues.Average();

            // Lower variance (more even distribution) = higher score
            var coefficientOfVariation = mean > 0 ? Math.Sqrt(variance) / mean : 0;
            return Math.Max(0, 1 - coefficientOfVariation);
        }

        private EntityOptimizationPotential CalculateEntityOptimizationPotential(EntitySnapshot entitySnapshot, List<TenantEntityUsage> tenantUsages)
        {
            var memoryWaste = entitySnapshot.TotalMemoryUsage * (1 - entitySnapshot.AverageHitRatio);
            var memorySavingsPotential = Math.Min(memoryWaste / entitySnapshot.TotalMemoryUsage, 0.5); // Max 50% savings

            var performanceGainPotential = Math.Max(0, 0.9 - entitySnapshot.AverageHitRatio); // Potential to reach 90% hit ratio

            var actions = new List<string>();
            if (memorySavingsPotential > 0.2) actions.Add("Reduce cache duration");
            if (performanceGainPotential > 0.1) actions.Add("Optimize cache strategy");
            if (tenantUsages.Any(t => t.HitRatio < 0.5)) actions.Add("Review tenant-specific patterns");

            var priority = (memorySavingsPotential + performanceGainPotential) switch
            {
                > 0.5 => 1,
                > 0.3 => 2,
                > 0.1 => 3,
                _ => 4
            };

            return new EntityOptimizationPotential
            {
                MemorySavingsPotential = memorySavingsPotential,
                PerformanceGainPotential = performanceGainPotential,
                OptimizationActions = actions,
                Priority = priority
            };
        }
    }
}
    }
}
