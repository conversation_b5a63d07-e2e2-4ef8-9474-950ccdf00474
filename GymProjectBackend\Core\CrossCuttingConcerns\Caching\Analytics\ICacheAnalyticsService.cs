using Core.CrossCuttingConcerns.Caching.Analytics.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching.Analytics
{
    public interface ICacheAnalyticsService
    {
        // Real-time monitoring
        Task<CacheAnalyticsSnapshot> GetCurrentSnapshotAsync();
        Task<List<CacheUsagePattern>> GetUsagePatternsAsync(int tenantId, TimeSpan timeRange);
        Task<MemoryUsageAnalysis> GetMemoryUsageAnalysisAsync();
        
        // Historical data
        Task<List<CacheMetricsHistory>> GetHistoricalDataAsync(DateTime startDate, DateTime endDate);
        Task<TenantCacheAnalysis> GetTenantAnalysisAsync(int tenantId, TimeSpan timeRange);
        
        // Pattern analysis
        Task<List<CacheHotspot>> GetCacheHotspotsAsync();
        Task<List<CacheInefficiency>> GetInefficienciesAsync();
        Task<CachePrediction> PredictMemoryUsageAsync(TimeSpan forecastPeriod);
        
        // Optimization recommendations
        Task<List<CacheOptimizationRecommendation>> GetOptimizationRecommendationsAsync();
        Task<CacheHealthScore> GetCacheHealthScoreAsync();
        
        // Data collection
        Task RecordCacheOperationAsync(CacheOperation operation);
        Task RecordMemorySnapshotAsync();
        Task CleanupOldDataAsync(TimeSpan retentionPeriod);
        
        // Configuration analysis
        Task<CacheConfigurationAnalysis> AnalyzeCacheConfigurationAsync();
        Task<List<EntityCacheAnalysis>> GetEntityAnalysisAsync();
    }
}
