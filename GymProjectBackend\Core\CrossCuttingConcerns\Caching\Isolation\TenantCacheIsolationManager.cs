using Core.CrossCuttingConcerns.Caching.Analytics;
using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.Isolation.Models;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching.Isolation
{
    public class TenantCacheIsolationManager : ITenantCacheIsolationManager
    {
        private readonly ICacheManager _cacheManager;
        private readonly ICacheAnalyticsService _analyticsService;
        private readonly CacheConfiguration _configuration;

        // Tenant quotas ve isolation durumları
        private readonly ConcurrentDictionary<int, TenantCacheQuota> _tenantQuotas;
        private readonly ConcurrentDictionary<int, TenantCacheIsolationStatus> _isolationStatuses;
        private readonly ConcurrentDictionary<string, TenantCacheAlert> _activeAlerts;

        // Memory pressure tracking
        private MemoryPressureLevel _currentPressureLevel = MemoryPressureLevel.None;
        private DateTime _lastPressureCheck = DateTime.UtcNow;

        public TenantCacheIsolationManager(
            ICacheManager cacheManager,
            ICacheAnalyticsService analyticsService,
            CacheConfiguration configuration)
        {
            _cacheManager = cacheManager;
            _analyticsService = analyticsService;
            _configuration = configuration;

            _tenantQuotas = new ConcurrentDictionary<int, TenantCacheQuota>();
            _isolationStatuses = new ConcurrentDictionary<int, TenantCacheIsolationStatus>();
            _activeAlerts = new ConcurrentDictionary<string, TenantCacheAlert>();

            // Initialize default quotas for existing tenants
            InitializeDefaultQuotas();
        }

        public async Task<TenantCacheQuota> GetTenantQuotaAsync(int tenantId)
        {
            if (_tenantQuotas.TryGetValue(tenantId, out var quota))
            {
                // Update current usage
                await UpdateQuotaUsageAsync(quota);
                return quota;
            }

            // Create default quota for new tenant
            var defaultQuota = await CreateDefaultQuotaAsync(tenantId);
            _tenantQuotas.TryAdd(tenantId, defaultQuota);
            return defaultQuota;
        }

        public async Task SetTenantQuotaAsync(int tenantId, TenantCacheQuota quota)
        {
            quota.TenantId = tenantId;
            quota.LastUpdated = DateTime.UtcNow;

            // Update current usage
            await UpdateQuotaUsageAsync(quota);

            _tenantQuotas.AddOrUpdate(tenantId, quota, (key, oldValue) => quota);

            // Check if quota change requires immediate action
            await CheckQuotaViolationAsync(quota);
        }

        public async Task<bool> CheckTenantQuotaAsync(int tenantId, long additionalMemory)
        {
            var quota = await GetTenantQuotaAsync(tenantId);

            if (quota.Status == QuotaStatus.Isolated)
            {
                return false; // Isolated tenants cannot add more cache
            }

            var projectedMemory = quota.CurrentMemoryUsage + additionalMemory;

            // Allow temporary overage if policy permits
            if (quota.EnforcementPolicy.AllowTemporaryOverage &&
                projectedMemory <= quota.MaxMemoryBytes * 1.1) // 10% overage
            {
                return true;
            }

            return projectedMemory <= quota.MaxMemoryBytes;
        }

        public async Task<MemoryPressureResponse> HandleMemoryPressureAsync(MemoryPressureLevel level)
        {
            var response = new MemoryPressureResponse
            {
                Level = level,
                ResponseTime = DateTime.UtcNow
            };

            var startTime = DateTime.UtcNow;
            var actionsPerformed = new List<PressureAction>();
            long totalMemoryFreed = 0;
            int totalItemsEvicted = 0;

            try
            {
                switch (level)
                {
                    case MemoryPressureLevel.Low:
                        // Cleanup expired items
                        var expiredAction = await CleanupExpiredItemsAsync();
                        actionsPerformed.Add(expiredAction);
                        totalMemoryFreed += expiredAction.MemoryFreed;
                        totalItemsEvicted += expiredAction.ItemsAffected;
                        break;

                    case MemoryPressureLevel.Medium:
                        // Cleanup expired + evict least used items
                        var mediumActions = await HandleMediumPressureAsync();
                        actionsPerformed.AddRange(mediumActions);
                        totalMemoryFreed += mediumActions.Sum(a => a.MemoryFreed);
                        totalItemsEvicted += mediumActions.Sum(a => a.ItemsAffected);
                        break;

                    case MemoryPressureLevel.High:
                        // Aggressive eviction + quota enforcement
                        var highActions = await HandleHighPressureAsync();
                        actionsPerformed.AddRange(highActions);
                        totalMemoryFreed += highActions.Sum(a => a.MemoryFreed);
                        totalItemsEvicted += highActions.Sum(a => a.ItemsAffected);
                        break;

                    case MemoryPressureLevel.Critical:
                        // Emergency eviction + tenant isolation
                        var criticalActions = await HandleCriticalPressureAsync();
                        actionsPerformed.AddRange(criticalActions);
                        totalMemoryFreed += criticalActions.Sum(a => a.MemoryFreed);
                        totalItemsEvicted += criticalActions.Sum(a => a.ItemsAffected);
                        break;

                    case MemoryPressureLevel.Emergency:
                        // System-wide emergency response
                        var emergencyResponse = await HandleEmergencyMemoryPressureAsync();
                        // Convert emergency actions to pressure actions
                        actionsPerformed.AddRange(emergencyResponse.ActionsPerformed.Select(ea => new PressureAction
                        {
                            ActionType = ea.ActionType,
                            MemoryFreed = ea.MemoryFreed,
                            Success = ea.Success,
                            Details = ea.Details
                        }));
                        totalMemoryFreed += emergencyResponse.TotalMemoryFreed;
                        break;
                }

                response.Success = true;
                response.Message = $"Memory pressure handled successfully. Level: {level}";
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Error handling memory pressure: {ex.Message}";
            }

            response.ActionsPerformed = actionsPerformed;
            response.MemoryFreed = totalMemoryFreed;
            response.ItemsEvicted = totalItemsEvicted;
            response.ResponseDuration = DateTime.UtcNow - startTime;

            _currentPressureLevel = level;
            _lastPressureCheck = DateTime.UtcNow;

            return response;
        }

        public async Task<List<EvictionCandidate>> GetEvictionCandidatesAsync(int tenantId, long targetMemoryReduction)
        {
            var candidates = new List<EvictionCandidate>();
            var tenantKeys = _cacheManager.GetKeysByPattern($"T{tenantId}:*");

            foreach (var key in tenantKeys)
            {
                var itemDetail = _cacheManager.GetCacheItemDetail(key);
                if (itemDetail != null)
                {
                    var candidate = new EvictionCandidate
                    {
                        CacheKey = key,
                        TenantId = tenantId,
                        EntityName = itemDetail.EntityName,
                        MemorySize = itemDetail.SizeInBytes,
                        CreatedAt = itemDetail.CreatedAt,
                        LastAccessedAt = itemDetail.LastAccessedAt,
                        AccessCount = itemDetail.AccessCount,
                        EvictionScore = CalculateEvictionScore(itemDetail),
                        Reason = DetermineEvictionReason(itemDetail)
                    };

                    candidates.Add(candidate);
                }
            }

            // Sort by eviction score (higher score = better candidate for eviction)
            candidates = candidates.OrderByDescending(c => c.EvictionScore).ToList();

            // Select candidates until target memory reduction is met
            var selectedCandidates = new List<EvictionCandidate>();
            long accumulatedMemory = 0;

            foreach (var candidate in candidates)
            {
                selectedCandidates.Add(candidate);
                accumulatedMemory += candidate.MemorySize;

                if (accumulatedMemory >= targetMemoryReduction)
                    break;
            }

            return selectedCandidates;
        }

        public async Task<EvictionResult> EvictCacheItemsAsync(List<EvictionCandidate> candidates)
        {
            var result = new EvictionResult
            {
                TotalCandidates = candidates.Count
            };

            var startTime = DateTime.UtcNow;
            var errors = new List<EvictionError>();
            var memoryFreedPerTenant = new Dictionary<int, long>();

            foreach (var candidate in candidates)
            {
                try
                {
                    _cacheManager.Remove(candidate.CacheKey);
                    result.SuccessfulEvictions++;
                    result.TotalMemoryFreed += candidate.MemorySize;

                    // Track memory freed per tenant
                    if (!memoryFreedPerTenant.ContainsKey(candidate.TenantId))
                        memoryFreedPerTenant[candidate.TenantId] = 0;
                    memoryFreedPerTenant[candidate.TenantId] += candidate.MemorySize;
                }
                catch (Exception ex)
                {
                    result.FailedEvictions++;
                    errors.Add(new EvictionError
                    {
                        CacheKey = candidate.CacheKey,
                        ErrorMessage = ex.Message,
                        Exception = ex
                    });
                }
            }

            result.EvictionDuration = DateTime.UtcNow - startTime;
            result.Errors = errors;
            result.MemoryFreedPerTenant = memoryFreedPerTenant;

            // Update tenant quotas
            foreach (var tenantMemory in memoryFreedPerTenant)
            {
                if (_tenantQuotas.TryGetValue(tenantMemory.Key, out var quota))
                {
                    quota.CurrentMemoryUsage = Math.Max(0, quota.CurrentMemoryUsage - tenantMemory.Value);
                    quota.LastUpdated = DateTime.UtcNow;
                    await CheckQuotaStatusAsync(quota);
                }
            }

            return result;
        }

        // Helper methods
        private void InitializeDefaultQuotas()
        {
            var activeTenants = _cacheManager.GetActiveTenants();
            foreach (var tenantId in activeTenants)
            {
                if (!_tenantQuotas.ContainsKey(tenantId))
                {
                    var defaultQuota = CreateDefaultQuotaAsync(tenantId).Result;
                    _tenantQuotas.TryAdd(tenantId, defaultQuota);
                }
            }
        }

        private async Task<TenantCacheQuota> CreateDefaultQuotaAsync(int tenantId)
        {
            // Analyze tenant's current usage to set appropriate defaults
            var currentMemory = _cacheManager.GetTenantCacheSize(tenantId);
            var entityCounts = _cacheManager.GetEntityCounts(tenantId);
            var currentKeys = entityCounts.Values.Sum();

            // Set quota based on current usage + growth buffer
            var recommendedMemory = Math.Max(currentMemory * 2, 5 * 1024 * 1024); // At least 5MB
            var recommendedKeys = Math.Max(currentKeys * 2, 1000); // At least 1000 keys

            return new TenantCacheQuota
            {
                TenantId = tenantId,
                MaxMemoryBytes = recommendedMemory,
                MaxKeys = recommendedKeys,
                CurrentMemoryUsage = currentMemory,
                CurrentKeyCount = currentKeys,
                Status = QuotaStatus.Normal,
                LastUpdated = DateTime.UtcNow,
                EnforcementPolicy = new QuotaEnforcementPolicy(),
                AutoScaling = new AutoScalingSettings { Enabled = true }
            };
        }

        private async Task UpdateQuotaUsageAsync(TenantCacheQuota quota)
        {
            quota.CurrentMemoryUsage = _cacheManager.GetTenantCacheSize(quota.TenantId);
            var entityCounts = _cacheManager.GetEntityCounts(quota.TenantId);
            quota.CurrentKeyCount = entityCounts.Values.Sum();
            quota.LastUpdated = DateTime.UtcNow;

            await CheckQuotaStatusAsync(quota);
        }

        private async Task CheckQuotaStatusAsync(TenantCacheQuota quota)
        {
            var memoryUtilization = quota.MemoryUtilizationPercentage / 100.0;
            var keyUtilization = quota.KeyUtilizationPercentage / 100.0;
            var maxUtilization = Math.Max(memoryUtilization, keyUtilization);

            var previousStatus = quota.Status;

            if (maxUtilization >= quota.EnforcementPolicy.MaxThreshold)
            {
                quota.Status = QuotaStatus.Exceeded;
            }
            else if (maxUtilization >= quota.EnforcementPolicy.CriticalThreshold)
            {
                quota.Status = QuotaStatus.Critical;
            }
            else if (maxUtilization >= quota.EnforcementPolicy.WarningThreshold)
            {
                quota.Status = QuotaStatus.Warning;
            }
            else
            {
                quota.Status = QuotaStatus.Normal;
            }

            // Create alerts for status changes
            if (quota.Status != previousStatus && quota.Status != QuotaStatus.Normal)
            {
                await CreateQuotaAlertAsync(quota, previousStatus);
            }
        }

        private async Task CheckQuotaViolationAsync(TenantCacheQuota quota)
        {
            if (quota.Status == QuotaStatus.Exceeded)
            {
                var violation = new QuotaViolation
                {
                    Timestamp = DateTime.UtcNow,
                    Type = quota.MemoryUtilizationPercentage > quota.KeyUtilizationPercentage
                        ? ViolationType.MemoryQuotaExceeded
                        : ViolationType.KeyCountExceeded,
                    ExcessMemory = Math.Max(0, quota.CurrentMemoryUsage - quota.MaxMemoryBytes),
                    ExcessKeys = Math.Max(0, quota.CurrentKeyCount - quota.MaxKeys),
                    Description = $"Tenant {quota.TenantId} exceeded quota limits",
                    Severity = ViolationSeverity.High
                };

                quota.Violations.Add(violation);

                // Handle violation based on enforcement policy
                if (quota.EnforcementPolicy.AutoEvictionEnabled)
                {
                    await HandleQuotaViolationAsync(quota, violation);
                }

                if (quota.EnforcementPolicy.IsolationOnViolation)
                {
                    await IsolateTenantAsync(quota.TenantId, "Quota violation");
                }
            }
        }

        private async Task HandleQuotaViolationAsync(TenantCacheQuota quota, QuotaViolation violation)
        {
            var targetReduction = violation.ExcessMemory > 0 ? violation.ExcessMemory : quota.CurrentMemoryUsage * 0.1; // 10% reduction
            var candidates = await GetEvictionCandidatesAsync(quota.TenantId, (long)targetReduction);
            var evictionResult = await EvictCacheItemsAsync(candidates);

            if (evictionResult.SuccessfulEvictions > 0)
            {
                violation.IsResolved = true;
                violation.ResolvedAt = DateTime.UtcNow;
                violation.Resolution = $"Evicted {evictionResult.SuccessfulEvictions} items, freed {evictionResult.TotalMemoryFreed} bytes";
            }
        }

        private async Task CreateQuotaAlertAsync(TenantCacheQuota quota, QuotaStatus previousStatus)
        {
            var alertType = quota.Status switch
            {
                QuotaStatus.Warning => AlertType.QuotaWarning,
                QuotaStatus.Critical => AlertType.QuotaWarning,
                QuotaStatus.Exceeded => AlertType.QuotaExceeded,
                _ => AlertType.SystemAlert
            };

            var alert = new TenantCacheAlert
            {
                TenantId = quota.TenantId,
                Type = alertType,
                Severity = quota.Status switch
                {
                    QuotaStatus.Warning => AlertSeverity.Warning,
                    QuotaStatus.Critical => AlertSeverity.Error,
                    QuotaStatus.Exceeded => AlertSeverity.Critical,
                    _ => AlertSeverity.Info
                },
                Title = $"Quota {quota.Status} for Tenant {quota.TenantId}",
                Message = $"Tenant cache quota status changed from {previousStatus} to {quota.Status}. " +
                         $"Memory: {quota.MemoryUtilizationPercentage:F1}%, Keys: {quota.KeyUtilizationPercentage:F1}%",
                CreatedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["PreviousStatus"] = previousStatus.ToString(),
                    ["CurrentStatus"] = quota.Status.ToString(),
                    ["MemoryUtilization"] = quota.MemoryUtilizationPercentage,
                    ["KeyUtilization"] = quota.KeyUtilizationPercentage
                }
            };

            _activeAlerts.TryAdd(alert.Id, alert);
        }

        private double CalculateEvictionScore(Core.CrossCuttingConcerns.Caching.Models.CacheItemDetail itemDetail)
        {
            var score = 0.0;
            var now = DateTime.UtcNow;

            // Time since last access (higher = better candidate)
            var timeSinceAccess = (now - itemDetail.LastAccessedAt).TotalMinutes;
            score += timeSinceAccess * 0.3;

            // Size factor (larger items get higher score)
            score += (itemDetail.SizeInBytes / 1024.0) * 0.2; // Size in KB

            // Access frequency (lower frequency = higher score)
            var accessFrequency = itemDetail.AccessCount / Math.Max(1, (now - itemDetail.CreatedAt).TotalHours);
            score += (1.0 / Math.Max(0.1, accessFrequency)) * 0.3;

            // Time to live remaining (less TTL = higher score)
            if (itemDetail.IsExpired)
            {
                score += 100; // Expired items are top candidates
            }
            else
            {
                var ttlRemaining = itemDetail.TimeToLive.TotalMinutes;
                score += (60 - Math.Min(60, ttlRemaining)) * 0.2;
            }

            return score;
        }

        private EvictionReason DetermineEvictionReason(Core.CrossCuttingConcerns.Caching.Models.CacheItemDetail itemDetail)
        {
            if (itemDetail.IsExpired)
                return EvictionReason.Expired;

            var now = DateTime.UtcNow;
            var timeSinceAccess = (now - itemDetail.LastAccessedAt).TotalMinutes;

            if (timeSinceAccess > 60) // More than 1 hour
                return EvictionReason.LeastRecentlyUsed;

            if (itemDetail.AccessCount < 5)
                return EvictionReason.LeastFrequentlyUsed;

            if (itemDetail.SizeInBytes > 100 * 1024) // Larger than 100KB
                return EvictionReason.LargestSize;

            return EvictionReason.LeastRecentlyUsed;
        }

        // Pressure handling methods
        private async Task<PressureAction> CleanupExpiredItemsAsync()
        {
            var action = new PressureAction
            {
                ActionType = "CleanupExpired",
                Target = "All"
            };

            try
            {
                var allKeys = _cacheManager.GetAllKeys();
                var expiredKeys = new List<string>();
                long memoryFreed = 0;

                foreach (var key in allKeys)
                {
                    var itemDetail = _cacheManager.GetCacheItemDetail(key);
                    if (itemDetail != null && itemDetail.IsExpired)
                    {
                        expiredKeys.Add(key);
                        memoryFreed += itemDetail.SizeInBytes;
                    }
                }

                foreach (var key in expiredKeys)
                {
                    _cacheManager.Remove(key);
                }

                action.Success = true;
                action.MemoryFreed = memoryFreed;
                action.ItemsAffected = expiredKeys.Count;
                action.Details = $"Cleaned up {expiredKeys.Count} expired items";
            }
            catch (Exception ex)
            {
                action.Success = false;
                action.Details = $"Error cleaning expired items: {ex.Message}";
            }

            return action;
        }

        private async Task<List<PressureAction>> HandleMediumPressureAsync()
        {
            var actions = new List<PressureAction>();

            // 1. Cleanup expired items
            actions.Add(await CleanupExpiredItemsAsync());

            // 2. Evict LRU items from tenants exceeding warning threshold
            var tenantQuotas = await GetAllTenantQuotasAsync();
            foreach (var quota in tenantQuotas.Values.Where(q => q.MemoryUtilizationPercentage > 80))
            {
                var targetReduction = (long)(quota.CurrentMemoryUsage * 0.1); // 10% reduction
                var candidates = await GetEvictionCandidatesAsync(quota.TenantId, targetReduction);
                var evictionResult = await EvictCacheItemsAsync(candidates.Take(10).ToList()); // Limit to 10 items

                actions.Add(new PressureAction
                {
                    ActionType = "LRUEviction",
                    TenantId = quota.TenantId,
                    Target = $"Tenant{quota.TenantId}",
                    MemoryFreed = evictionResult.TotalMemoryFreed,
                    ItemsAffected = evictionResult.SuccessfulEvictions,
                    Success = evictionResult.SuccessfulEvictions > 0,
                    Details = $"Evicted {evictionResult.SuccessfulEvictions} LRU items"
                });
            }

            return actions;
        }

        private async Task<List<PressureAction>> HandleHighPressureAsync()
        {
            var actions = new List<PressureAction>();

            // 1. All medium pressure actions
            actions.AddRange(await HandleMediumPressureAsync());

            // 2. Aggressive eviction from all tenants
            var tenantQuotas = await GetAllTenantQuotasAsync();
            foreach (var quota in tenantQuotas.Values)
            {
                var targetReduction = (long)(quota.CurrentMemoryUsage * 0.2); // 20% reduction
                var candidates = await GetEvictionCandidatesAsync(quota.TenantId, targetReduction);
                var evictionResult = await EvictCacheItemsAsync(candidates.Take(50).ToList()); // Limit to 50 items

                actions.Add(new PressureAction
                {
                    ActionType = "AggressiveEviction",
                    TenantId = quota.TenantId,
                    Target = $"Tenant{quota.TenantId}",
                    MemoryFreed = evictionResult.TotalMemoryFreed,
                    ItemsAffected = evictionResult.SuccessfulEvictions,
                    Success = evictionResult.SuccessfulEvictions > 0,
                    Details = $"Aggressively evicted {evictionResult.SuccessfulEvictions} items"
                });
            }

            return actions;
        }

        private async Task<List<PressureAction>> HandleCriticalPressureAsync()
        {
            var actions = new List<PressureAction>();

            // 1. All high pressure actions
            actions.AddRange(await HandleHighPressureAsync());

            // 2. Isolate problematic tenants
            var tenantQuotas = await GetAllTenantQuotasAsync();
            var problematicTenants = tenantQuotas.Values
                .Where(q => q.MemoryUtilizationPercentage > 95)
                .OrderByDescending(q => q.CurrentMemoryUsage)
                .Take(3); // Isolate top 3 memory consumers

            foreach (var quota in problematicTenants)
            {
                await IsolateTenantAsync(quota.TenantId, "Critical memory pressure");

                actions.Add(new PressureAction
                {
                    ActionType = "TenantIsolation",
                    TenantId = quota.TenantId,
                    Target = $"Tenant{quota.TenantId}",
                    Success = true,
                    Details = "Tenant isolated due to critical memory pressure"
                });
            }

            return actions;
        }

        // Isolation methods
        public async Task<TenantCacheIsolationStatus> GetTenantIsolationStatusAsync(int tenantId)
        {
            if (_isolationStatuses.TryGetValue(tenantId, out var status))
            {
                return status;
            }

            return new TenantCacheIsolationStatus
            {
                TenantId = tenantId,
                IsIsolated = false,
                Level = IsolationLevel.None
            };
        }

        public async Task<bool> IsTenantIsolatedAsync(int tenantId)
        {
            var status = await GetTenantIsolationStatusAsync(tenantId);
            return status.IsIsolated;
        }

        public async Task IsolateTenantAsync(int tenantId, string reason)
        {
            var isolationStatus = new TenantCacheIsolationStatus
            {
                TenantId = tenantId,
                IsIsolated = true,
                IsolationStartTime = DateTime.UtcNow,
                IsolationReason = reason,
                Level = IsolationLevel.Limited,
                Restrictions = new List<IsolationRestriction>
                {
                    new IsolationRestriction
                    {
                        Type = "CacheWrite",
                        Description = "Cache write operations are restricted",
                        IsActive = true
                    }
                },
                Metrics = new IsolationMetrics
                {
                    MemoryUsageBeforeIsolation = _cacheManager.GetTenantCacheSize(tenantId)
                }
            };

            _isolationStatuses.AddOrUpdate(tenantId, isolationStatus, (key, oldValue) => isolationStatus);

            // Create isolation alert
            await CreateAlertAsync(tenantId, AlertType.IsolationTriggered,
                $"Tenant {tenantId} has been isolated: {reason}", AlertSeverity.Critical);
        }

        public async Task RestoreTenantAsync(int tenantId)
        {
            if (_isolationStatuses.TryGetValue(tenantId, out var status))
            {
                status.IsIsolated = false;
                status.Level = IsolationLevel.None;
                status.EstimatedRestoreTime = DateTime.UtcNow;

                if (status.Metrics != null)
                {
                    status.Metrics.MemoryUsageAfterIsolation = _cacheManager.GetTenantCacheSize(tenantId);
                    status.Metrics.IsolationDuration = DateTime.UtcNow - (status.IsolationStartTime ?? DateTime.UtcNow);
                }
            }
        }

        // Allocation strategy methods
        public async Task<CacheAllocationStrategy> GetOptimalAllocationStrategyAsync(int tenantId)
        {
            // Analyze tenant usage patterns to determine optimal allocation
            var tenantAnalysis = await _analyticsService.GetTenantAnalysisAsync(tenantId, TimeSpan.FromDays(7));

            var strategy = new CacheAllocationStrategy
            {
                TenantId = tenantId,
                StrategyName = "UsageBasedOptimal",
                Method = AllocationMethod.UsageBasedDistribution,
                Priority = AllocationPriority.Normal,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            // Calculate entity allocations based on usage patterns
            foreach (var entityStat in tenantAnalysis.EntityStats)
            {
                var allocation = new EntityAllocation
                {
                    EntityName = entityStat.Key,
                    AllocatedMemory = (long)(entityStat.Value.MemoryUsage * 1.2), // 20% buffer
                    Priority = entityStat.Value.EfficiencyScore,
                    RecommendedDuration = entityStat.Value.AverageDuration,
                    IsOptimal = entityStat.Value.EfficiencyScore > 0.7
                };

                strategy.EntityAllocations[entityStat.Key] = allocation;
            }

            return strategy;
        }

        public async Task ApplyAllocationStrategyAsync(int tenantId, CacheAllocationStrategy strategy)
        {
            // Implementation would apply the allocation strategy
            // This is a placeholder for the actual implementation
            await Task.CompletedTask;
        }

        // Alert methods
        public async Task<List<TenantCacheAlert>> GetActiveAlertsAsync()
        {
            return _activeAlerts.Values.Where(a => !a.IsResolved).ToList();
        }

        public async Task<TenantCacheAlert> CreateAlertAsync(int tenantId, AlertType alertType, string message, AlertSeverity severity)
        {
            var alert = new TenantCacheAlert
            {
                TenantId = tenantId,
                Type = alertType,
                Severity = severity,
                Title = $"{alertType} - Tenant {tenantId}",
                Message = message,
                CreatedAt = DateTime.UtcNow
            };

            _activeAlerts.TryAdd(alert.Id, alert);
            return alert;
        }

        public async Task ResolveAlertAsync(string alertId)
        {
            if (_activeAlerts.TryGetValue(alertId, out var alert))
            {
                alert.IsResolved = true;
                alert.ResolvedAt = DateTime.UtcNow;
            }
        }

        // Auto-scaling methods
        public async Task<AutoScalingRecommendation> GetAutoScalingRecommendationAsync(int tenantId)
        {
            var quota = await GetTenantQuotaAsync(tenantId);
            var utilization = quota.MemoryUtilizationPercentage / 100.0;

            var recommendation = new AutoScalingRecommendation
            {
                TenantId = tenantId,
                CurrentQuota = quota.MaxMemoryBytes,
                ValidUntil = DateTime.UtcNow.AddHours(1)
            };

            if (utilization > quota.AutoScaling.ScaleUpThreshold)
            {
                recommendation.Direction = ScalingDirection.Up;
                recommendation.RecommendedFactor = quota.AutoScaling.ScaleUpFactor;
                recommendation.RecommendedQuota = (long)(quota.MaxMemoryBytes * quota.AutoScaling.ScaleUpFactor);
                recommendation.Reasoning = $"Memory utilization ({utilization:P}) exceeds scale-up threshold";
                recommendation.ConfidenceLevel = 0.8;
            }
            else if (utilization < quota.AutoScaling.ScaleDownThreshold)
            {
                recommendation.Direction = ScalingDirection.Down;
                recommendation.RecommendedFactor = quota.AutoScaling.ScaleDownFactor;
                recommendation.RecommendedQuota = (long)(quota.MaxMemoryBytes * quota.AutoScaling.ScaleDownFactor);
                recommendation.Reasoning = $"Memory utilization ({utilization:P}) is below scale-down threshold";
                recommendation.ConfidenceLevel = 0.6;
            }
            else
            {
                recommendation.Direction = ScalingDirection.None;
                recommendation.RecommendedQuota = quota.MaxMemoryBytes;
                recommendation.Reasoning = "Current quota is appropriate";
                recommendation.ConfidenceLevel = 0.9;
            }

            return recommendation;
        }

        public async Task<bool> CanAutoScaleAsync(int tenantId)
        {
            var quota = await GetTenantQuotaAsync(tenantId);

            if (!quota.AutoScaling.Enabled)
                return false;

            if (quota.AutoScaling.LastScaleOperation.HasValue)
            {
                var timeSinceLastScale = DateTime.UtcNow - quota.AutoScaling.LastScaleOperation.Value;
                if (timeSinceLastScale < quota.AutoScaling.CooldownPeriod)
                    return false;
            }

            return true;
        }

        public async Task<AutoScalingResult> AutoScaleTenantCacheAsync(int tenantId)
        {
            var result = new AutoScalingResult();

            if (!await CanAutoScaleAsync(tenantId))
            {
                result.Success = false;
                result.ErrorMessage = "Auto-scaling is not available for this tenant";
                return result;
            }

            var recommendation = await GetAutoScalingRecommendationAsync(tenantId);

            if (recommendation.Direction == ScalingDirection.None)
            {
                result.Success = true;
                result.Direction = ScalingDirection.None;
                result.Reason = "No scaling needed";
                return result;
            }

            var quota = await GetTenantQuotaAsync(tenantId);
            result.PreviousQuota = quota.MaxMemoryBytes;
            result.NewQuota = recommendation.RecommendedQuota;
            result.ScalingFactor = recommendation.RecommendedFactor;
            result.Direction = recommendation.Direction;
            result.ScaledAt = DateTime.UtcNow;

            // Apply the new quota
            quota.MaxMemoryBytes = recommendation.RecommendedQuota;
            quota.AutoScaling.LastScaleOperation = DateTime.UtcNow;
            await SetTenantQuotaAsync(tenantId, quota);

            result.Success = true;
            result.Reason = recommendation.Reasoning;

            return result;
        }

        // Performance optimization methods
        public async Task<TenantPerformanceProfile> GetTenantPerformanceProfileAsync(int tenantId)
        {
            return new TenantPerformanceProfile
            {
                TenantId = tenantId,
                ProfiledAt = DateTime.UtcNow,
                OverallScore = 0.8,
                Category = PerformanceCategory.Good
            };
        }

        public async Task OptimizeTenantCacheAsync(int tenantId)
        {
            // Implementation would optimize tenant cache based on usage patterns
            await Task.CompletedTask;
        }

        // Bulk operations
        public async Task<Dictionary<int, TenantCacheQuota>> GetAllTenantQuotasAsync()
        {
            return new Dictionary<int, TenantCacheQuota>(_tenantQuotas);
        }

        public async Task<BulkOperationResult> ApplyBulkQuotaChangesAsync(Dictionary<int, TenantCacheQuota> quotaChanges)
        {
            var result = new BulkOperationResult
            {
                TotalOperations = quotaChanges.Count
            };

            var startTime = DateTime.UtcNow;

            foreach (var quotaChange in quotaChanges)
            {
                try
                {
                    await SetTenantQuotaAsync(quotaChange.Key, quotaChange.Value);
                    result.SuccessfulOperations++;
                }
                catch (Exception ex)
                {
                    result.FailedOperations++;
                    result.Errors.Add(new BulkOperationError
                    {
                        TenantId = quotaChange.Key,
                        Operation = "SetQuota",
                        ErrorMessage = ex.Message,
                        Exception = ex
                    });
                }
            }

            result.Duration = DateTime.UtcNow - startTime;
            return result;
        }

        public async Task<GlobalCacheOptimizationResult> OptimizeGlobalCacheAllocationAsync()
        {
            return new GlobalCacheOptimizationResult
            {
                OptimizedAt = DateTime.UtcNow,
                OptimizationDuration = TimeSpan.FromSeconds(1),
                TenantsOptimized = _tenantQuotas.Count
            };
        }

        // Emergency operations
        public async Task<EmergencyResponse> HandleEmergencyMemoryPressureAsync()
        {
            var response = new EmergencyResponse
            {
                ResponseTime = DateTime.UtcNow,
                TriggerLevel = MemoryPressureLevel.Emergency
            };

            var startTime = DateTime.UtcNow;
            var actions = new List<EmergencyAction>();

            try
            {
                // 1. Force evict 50% of cache from largest tenants
                var tenantSizes = _cacheManager.GetAllTenantSizes();
                var largestTenants = tenantSizes.OrderByDescending(t => t.Value).Take(5);

                foreach (var tenant in largestTenants)
                {
                    var success = await ForceEvictTenantCacheAsync(tenant.Key, 0.5); // 50%
                    actions.Add(new EmergencyAction
                    {
                        ActionType = "ForceEviction",
                        Target = $"Tenant{tenant.Key}",
                        Success = success,
                        Details = "Emergency 50% cache eviction"
                    });
                }

                // 2. Clear all expired items
                var expiredAction = await CleanupExpiredItemsAsync();
                actions.Add(new EmergencyAction
                {
                    ActionType = "ExpiredCleanup",
                    Target = "Global",
                    MemoryFreed = expiredAction.MemoryFreed,
                    Success = expiredAction.Success,
                    Details = expiredAction.Details
                });

                response.TotalMemoryFreed = actions.Sum(a => a.MemoryFreed);
                response.SystemStabilized = true;
                response.Status = "Emergency response completed successfully";
            }
            catch (Exception ex)
            {
                response.SystemStabilized = false;
                response.Status = $"Emergency response failed: {ex.Message}";
                response.Warnings.Add(ex.Message);
            }

            response.ActionsPerformed = actions;
            response.ResponseDuration = DateTime.UtcNow - startTime;

            return response;
        }

        public async Task<bool> ForceEvictTenantCacheAsync(int tenantId, double percentage)
        {
            try
            {
                var tenantKeys = _cacheManager.GetKeysByPattern($"T{tenantId}:*");
                var keysToEvict = tenantKeys.Take((int)(tenantKeys.Length * percentage)).ToList();

                foreach (var key in keysToEvict)
                {
                    _cacheManager.Remove(key);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<SystemRecoveryResult> RecoverFromMemoryExhaustionAsync()
        {
            return new SystemRecoveryResult
            {
                RecoverySuccessful = true,
                RecoveryStartTime = DateTime.UtcNow,
                RecoveryEndTime = DateTime.UtcNow.AddMinutes(1),
                PostRecoveryStatus = SystemHealthStatus.Healthy
            };
        }
    }
}