using System;
using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching.Isolation.Models
{
    public class TenantCacheQuota
    {
        public int TenantId { get; set; }
        public long MaxMemoryBytes { get; set; }
        public int MaxKeys { get; set; }
        public long CurrentMemoryUsage { get; set; }
        public int CurrentKeyCount { get; set; }
        public double MemoryUtilizationPercentage => MaxMemoryBytes > 0 ? (double)CurrentMemoryUsage / MaxMemoryBytes * 100 : 0;
        public double KeyUtilizationPercentage => MaxKeys > 0 ? (double)CurrentKeyCount / MaxKeys * 100 : 0;
        public QuotaStatus Status { get; set; }
        public DateTime LastUpdated { get; set; }
        public QuotaEnforcementPolicy EnforcementPolicy { get; set; }
        public List<QuotaViolation> Violations { get; set; } = new();
        public AutoScalingSettings AutoScaling { get; set; }
    }

    public enum QuotaStatus
    {
        Normal,
        Warning,
        Critical,
        Exceeded,
        Isolated
    }

    public class QuotaEnforcementPolicy
    {
        public double WarningThreshold { get; set; } = 0.8; // 80%
        public double CriticalThreshold { get; set; } = 0.9; // 90%
        public double MaxThreshold { get; set; } = 1.0; // 100%
        public EvictionPolicy EvictionPolicy { get; set; } = EvictionPolicy.LRU;
        public bool AllowTemporaryOverage { get; set; } = true;
        public TimeSpan TemporaryOverageWindow { get; set; } = TimeSpan.FromMinutes(5);
        public bool AutoEvictionEnabled { get; set; } = true;
        public bool IsolationOnViolation { get; set; } = false;
    }

    public enum EvictionPolicy
    {
        LRU,        // Least Recently Used
        LFU,        // Least Frequently Used
        TTL,        // Time To Live based
        Size,       // Largest items first
        Priority,   // Based on cache priority
        Hybrid      // Combination of multiple factors
    }

    public class QuotaViolation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; }
        public ViolationType Type { get; set; }
        public long ExcessMemory { get; set; }
        public int ExcessKeys { get; set; }
        public string Description { get; set; }
        public ViolationSeverity Severity { get; set; }
        public bool IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string Resolution { get; set; }
    }

    public enum ViolationType
    {
        MemoryQuotaExceeded,
        KeyCountExceeded,
        RapidGrowth,
        SustainedOverage,
        PerformanceDegradation
    }

    public enum ViolationSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    public class AutoScalingSettings
    {
        public bool Enabled { get; set; } = false;
        public double ScaleUpThreshold { get; set; } = 0.85; // 85%
        public double ScaleDownThreshold { get; set; } = 0.3; // 30%
        public double ScaleUpFactor { get; set; } = 1.5; // 50% increase
        public double ScaleDownFactor { get; set; } = 0.8; // 20% decrease
        public long MinMemoryBytes { get; set; } = 1024 * 1024; // 1MB
        public long MaxMemoryBytes { get; set; } = 100 * 1024 * 1024; // 100MB
        public TimeSpan CooldownPeriod { get; set; } = TimeSpan.FromMinutes(10);
        public DateTime? LastScaleOperation { get; set; }
    }

    public enum MemoryPressureLevel
    {
        None,
        Low,
        Medium,
        High,
        Critical,
        Emergency
    }

    public class MemoryPressureResponse
    {
        public MemoryPressureLevel Level { get; set; }
        public DateTime ResponseTime { get; set; }
        public List<PressureAction> ActionsPerformed { get; set; } = new();
        public long MemoryFreed { get; set; }
        public int ItemsEvicted { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; }
        public TimeSpan ResponseDuration { get; set; }
    }

    public class PressureAction
    {
        public string ActionType { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public long MemoryFreed { get; set; }
        public int ItemsAffected { get; set; }
        public bool Success { get; set; }
        public string Details { get; set; }
    }

    public class EvictionCandidate
    {
        public string CacheKey { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public long MemorySize { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastAccessedAt { get; set; }
        public int AccessCount { get; set; }
        public double EvictionScore { get; set; }
        public EvictionReason Reason { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public enum EvictionReason
    {
        LeastRecentlyUsed,
        LeastFrequentlyUsed,
        Expired,
        LargestSize,
        LowestPriority,
        QuotaViolation,
        MemoryPressure,
        ManualEviction
    }

    public class EvictionResult
    {
        public int TotalCandidates { get; set; }
        public int SuccessfulEvictions { get; set; }
        public int FailedEvictions { get; set; }
        public long TotalMemoryFreed { get; set; }
        public TimeSpan EvictionDuration { get; set; }
        public List<EvictionError> Errors { get; set; } = new();
        public Dictionary<int, long> MemoryFreedPerTenant { get; set; } = new();
    }

    public class EvictionError
    {
        public string CacheKey { get; set; }
        public string ErrorMessage { get; set; }
        public Exception Exception { get; set; }
    }

    public class TenantCacheIsolationStatus
    {
        public int TenantId { get; set; }
        public bool IsIsolated { get; set; }
        public DateTime? IsolationStartTime { get; set; }
        public string IsolationReason { get; set; }
        public IsolationLevel Level { get; set; }
        public List<IsolationRestriction> Restrictions { get; set; } = new();
        public IsolationMetrics Metrics { get; set; }
        public DateTime? EstimatedRestoreTime { get; set; }
        public List<string> RestoreConditions { get; set; } = new();
    }

    public enum IsolationLevel
    {
        None,
        ReadOnly,
        Limited,
        Quarantined,
        Suspended
    }

    public class IsolationRestriction
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    public class IsolationMetrics
    {
        public long MemoryUsageBeforeIsolation { get; set; }
        public long MemoryUsageAfterIsolation { get; set; }
        public double PerformanceImpact { get; set; }
        public int BlockedOperations { get; set; }
        public TimeSpan IsolationDuration { get; set; }
    }

    public class CacheAllocationStrategy
    {
        public int TenantId { get; set; }
        public string StrategyName { get; set; }
        public AllocationMethod Method { get; set; }
        public Dictionary<string, EntityAllocation> EntityAllocations { get; set; } = new();
        public AllocationPriority Priority { get; set; }
        public double EfficiencyScore { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
        public AllocationConstraints Constraints { get; set; }
    }

    public enum AllocationMethod
    {
        EqualDistribution,
        UsageBasedDistribution,
        PriorityBasedDistribution,
        PerformanceOptimized,
        MemoryOptimized,
        Adaptive
    }

    public class EntityAllocation
    {
        public string EntityName { get; set; }
        public long AllocatedMemory { get; set; }
        public int AllocatedKeys { get; set; }
        public double Priority { get; set; }
        public TimeSpan RecommendedDuration { get; set; }
        public string[] RecommendedTags { get; set; }
        public bool IsOptimal { get; set; }
    }

    public enum AllocationPriority
    {
        Low,
        Normal,
        High,
        Critical
    }

    public class AllocationConstraints
    {
        public long MinMemoryPerEntity { get; set; } = 1024; // 1KB
        public long MaxMemoryPerEntity { get; set; } = 10 * 1024 * 1024; // 10MB
        public int MinKeysPerEntity { get; set; } = 1;
        public int MaxKeysPerEntity { get; set; } = 1000;
        public List<string> RestrictedEntities { get; set; } = new();
        public List<string> PriorityEntities { get; set; } = new();
    }

    public class TenantCacheAlert
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public int TenantId { get; set; }
        public AlertType Type { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public bool IsResolved { get; set; }
        public string ResolvedBy { get; set; }
        public string Resolution { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<AlertAction> SuggestedActions { get; set; } = new();
    }

    public enum AlertType
    {
        QuotaWarning,
        QuotaExceeded,
        MemoryPressure,
        PerformanceDegradation,
        IsolationTriggered,
        AutoScalingFailed,
        ConfigurationIssue,
        SystemAlert
    }

    public enum AlertSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    public class AlertAction
    {
        public string ActionType { get; set; }
        public string Description { get; set; }
        public bool IsAutomated { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class AutoScalingRecommendation
    {
        public int TenantId { get; set; }
        public ScalingDirection Direction { get; set; }
        public double RecommendedFactor { get; set; }
        public long CurrentQuota { get; set; }
        public long RecommendedQuota { get; set; }
        public string Reasoning { get; set; }
        public double ConfidenceLevel { get; set; }
        public List<string> Prerequisites { get; set; } = new();
        public List<string> RiskFactors { get; set; } = new();
        public DateTime ValidUntil { get; set; }
    }

    public enum ScalingDirection
    {
        None,
        Up,
        Down
    }

    public class AutoScalingResult
    {
        public bool Success { get; set; }
        public ScalingDirection Direction { get; set; }
        public long PreviousQuota { get; set; }
        public long NewQuota { get; set; }
        public double ScalingFactor { get; set; }
        public string Reason { get; set; }
        public DateTime ScaledAt { get; set; }
        public List<string> Warnings { get; set; } = new();
        public string ErrorMessage { get; set; }
    }

    public class TenantPerformanceProfile
    {
        public int TenantId { get; set; }
        public DateTime ProfiledAt { get; set; }
        public TimeSpan ProfilePeriod { get; set; }
        public PerformanceMetrics Metrics { get; set; }
        public List<PerformanceBottleneck> Bottlenecks { get; set; } = new();
        public List<PerformanceRecommendation> Recommendations { get; set; } = new();
        public double OverallScore { get; set; }
        public PerformanceCategory Category { get; set; }
    }

    public class PerformanceMetrics
    {
        public double AverageHitRatio { get; set; }
        public long AverageResponseTime { get; set; }
        public double MemoryEfficiency { get; set; }
        public int TotalOperations { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double ErrorRate { get; set; }
        public Dictionary<string, double> EntityPerformance { get; set; } = new();
    }

    public class PerformanceBottleneck
    {
        public string Type { get; set; }
        public string EntityName { get; set; }
        public string Description { get; set; }
        public double ImpactScore { get; set; }
        public List<string> Causes { get; set; } = new();
        public List<string> Solutions { get; set; } = new();
    }

    public class PerformanceRecommendation
    {
        public string Action { get; set; }
        public string Target { get; set; }
        public string Description { get; set; }
        public double ExpectedImprovement { get; set; }
        public int Priority { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public enum PerformanceCategory
    {
        Excellent,
        Good,
        Average,
        Poor,
        Critical
    }

    public class BulkOperationResult
    {
        public int TotalOperations { get; set; }
        public int SuccessfulOperations { get; set; }
        public int FailedOperations { get; set; }
        public TimeSpan Duration { get; set; }
        public List<BulkOperationError> Errors { get; set; } = new();
        public Dictionary<string, object> Summary { get; set; } = new();
    }

    public class BulkOperationError
    {
        public int TenantId { get; set; }
        public string Operation { get; set; }
        public string ErrorMessage { get; set; }
        public Exception Exception { get; set; }
    }

    public class GlobalCacheOptimizationResult
    {
        public DateTime OptimizedAt { get; set; }
        public TimeSpan OptimizationDuration { get; set; }
        public long TotalMemoryBefore { get; set; }
        public long TotalMemoryAfter { get; set; }
        public long MemorySaved { get; set; }
        public double EfficiencyImprovement { get; set; }
        public int TenantsOptimized { get; set; }
        public List<TenantOptimizationResult> TenantResults { get; set; } = new();
        public List<string> GlobalRecommendations { get; set; } = new();
    }

    public class TenantOptimizationResult
    {
        public int TenantId { get; set; }
        public long MemoryBefore { get; set; }
        public long MemoryAfter { get; set; }
        public double PerformanceImprovement { get; set; }
        public List<string> ActionsPerformed { get; set; } = new();
    }

    public class EmergencyResponse
    {
        public DateTime ResponseTime { get; set; }
        public MemoryPressureLevel TriggerLevel { get; set; }
        public List<EmergencyAction> ActionsPerformed { get; set; } = new();
        public long TotalMemoryFreed { get; set; }
        public bool SystemStabilized { get; set; }
        public TimeSpan ResponseDuration { get; set; }
        public string Status { get; set; }
        public List<string> Warnings { get; set; } = new();
    }

    public class EmergencyAction
    {
        public string ActionType { get; set; }
        public string Target { get; set; }
        public long MemoryFreed { get; set; }
        public bool Success { get; set; }
        public string Details { get; set; }
        public TimeSpan Duration { get; set; }
    }

    public class SystemRecoveryResult
    {
        public bool RecoverySuccessful { get; set; }
        public DateTime RecoveryStartTime { get; set; }
        public DateTime? RecoveryEndTime { get; set; }
        public TimeSpan RecoveryDuration { get; set; }
        public List<RecoveryStep> Steps { get; set; } = new();
        public SystemHealthStatus PostRecoveryStatus { get; set; }
        public List<string> RemainingIssues { get; set; } = new();
        public List<string> PreventiveMeasures { get; set; } = new();
    }

    public class RecoveryStep
    {
        public string StepName { get; set; }
        public string Description { get; set; }
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public string Result { get; set; }
        public List<string> Warnings { get; set; } = new();
    }

    public enum SystemHealthStatus
    {
        Healthy,
        Degraded,
        Unstable,
        Critical,
        Recovering
    }
}
