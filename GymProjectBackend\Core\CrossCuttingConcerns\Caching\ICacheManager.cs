﻿using Core.CrossCuttingConcerns.Caching.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    public interface ICacheManager
    {
        // Temel cache operasyonları
        T Get<T>(string key);
        object Get(string key);
        void Add(string key, object value, int durationMinutes);
        void Add(string key, object value, int durationMinutes, string[] tags);
        bool IsAdd(string key);
        void Remove(string key);
        void RemoveByPattern(string pattern);
        void RemoveByTags(string[] tags);

        // Multi-tenant operasyonları
        void RemoveByTenant(int tenantId);
        void RemoveByEntity(int tenantId, string entityName);
        void RemoveByEntityAndOperation(int tenantId, string entityName, string operation);

        // Bulk operasyonları
        Dictionary<string, T> GetMultiple<T>(string[] keys);
        void AddMultiple(Dictionary<string, object> items, int durationMinutes);
        void RemoveMultiple(string[] keys);

        // Cache yönetimi
        void Clear();
        void ClearTenant(int tenantId);
        long GetCacheSize();
        long GetCacheItemCount();
        string[] GetAllKeys();
        string[] GetKeysByPattern(string pattern);

        // İstatistikler
        CacheStatistics GetStatistics();
        void ResetStatistics();

        // Health check
        bool IsHealthy();
        Dictionary<string, object> GetHealthInfo();

        // Gelişmiş analiz metodları
        List<int> GetActiveTenants();
        Dictionary<string, int> GetEntityCounts(int tenantId);
        List<CacheItemDetail> GetCacheDetails(int tenantId);
        CacheItemDetail GetCacheItemDetail(string key);
        long GetTenantCacheSize(int tenantId);
        Dictionary<int, long> GetAllTenantSizes();
        Dictionary<string, object> GetPerformanceMetrics();

        // Gelişmiş yönetim
        List<int> GetActiveTenants();
        Dictionary<string, int> GetEntityCounts(int tenantId);
        List<CacheItemDetail> GetCacheDetails(int tenantId);
        CacheItemDetail GetCacheItemDetail(string key);
        long GetTenantCacheSize(int tenantId);
        Dictionary<int, long> GetAllTenantSizes();
        Dictionary<string, object>    GetPerformanceMetrics();
    }
}
