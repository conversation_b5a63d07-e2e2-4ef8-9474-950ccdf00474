using System;
using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching.Analytics.Models
{
    public class CacheAnalyticsSnapshot
    {
        public DateTime Timestamp { get; set; }
        public long TotalMemoryUsage { get; set; }
        public long CacheMemoryUsage { get; set; }
        public double MemoryPressureLevel { get; set; }
        public int TotalKeys { get; set; }
        public int ActiveTenants { get; set; }
        public double OverallHitRatio { get; set; }
        public Dictionary<int, TenantSnapshot> TenantSnapshots { get; set; } = new();
        public Dictionary<string, EntitySnapshot> EntitySnapshots { get; set; } = new();
    }

    public class TenantSnapshot
    {
        public int TenantId { get; set; }
        public long MemoryUsage { get; set; }
        public int KeyCount { get; set; }
        public double HitRatio { get; set; }
        public DateTime LastActivity { get; set; }
        public Dictionary<string, int> EntityCounts { get; set; } = new();
    }

    public class EntitySnapshot
    {
        public string EntityName { get; set; }
        public long TotalMemoryUsage { get; set; }
        public int TotalKeys { get; set; }
        public double AverageHitRatio { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public int TenantsUsingEntity { get; set; }
    }

    public class CacheUsagePattern
    {
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public string OperationType { get; set; }
        public DateTime TimeWindow { get; set; }
        public int OperationCount { get; set; }
        public double HitRatio { get; set; }
        public long AverageResponseTime { get; set; }
        public long MemoryImpact { get; set; }
    }

    public class MemoryUsageAnalysis
    {
        public DateTime AnalysisTime { get; set; }
        public long TotalSystemMemory { get; set; }
        public long CacheMemoryUsage { get; set; }
        public double MemoryPressurePercentage { get; set; }
        public string PressureLevel { get; set; }
        public List<MemoryHotspot> Hotspots { get; set; } = new();
        public MemoryTrend Trend { get; set; }
        public long EstimatedOptimalSize { get; set; }
    }

    public class MemoryHotspot
    {
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public long MemoryUsage { get; set; }
        public double PercentageOfTotal { get; set; }
        public int KeyCount { get; set; }
        public double EfficiencyScore { get; set; }
    }

    public class MemoryTrend
    {
        public TrendDirection Direction { get; set; }
        public double GrowthRate { get; set; }
        public DateTime PredictedFullCapacity { get; set; }
        public List<MemoryDataPoint> DataPoints { get; set; } = new();
    }

    public class MemoryDataPoint
    {
        public DateTime Timestamp { get; set; }
        public long MemoryUsage { get; set; }
        public int KeyCount { get; set; }
    }

    public enum TrendDirection
    {
        Stable,
        Growing,
        Declining,
        Volatile
    }

    public class CacheMetricsHistory
    {
        public DateTime Timestamp { get; set; }
        public long MemoryUsage { get; set; }
        public int KeyCount { get; set; }
        public double HitRatio { get; set; }
        public int ActiveTenants { get; set; }
        public double MemoryPressure { get; set; }
        public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
    }

    public class TenantCacheAnalysis
    {
        public int TenantId { get; set; }
        public TimeSpan AnalysisPeriod { get; set; }
        public long AverageMemoryUsage { get; set; }
        public long PeakMemoryUsage { get; set; }
        public double AverageHitRatio { get; set; }
        public int TotalOperations { get; set; }
        public Dictionary<string, EntityUsageStats> EntityStats { get; set; } = new();
        public List<CacheUsagePattern> UsagePatterns { get; set; } = new();
        public TenantHealthScore HealthScore { get; set; }
    }

    public class EntityUsageStats
    {
        public string EntityName { get; set; }
        public long MemoryUsage { get; set; }
        public int KeyCount { get; set; }
        public double HitRatio { get; set; }
        public int AccessCount { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public double EfficiencyScore { get; set; }
    }

    public class TenantHealthScore
    {
        public double OverallScore { get; set; }
        public double MemoryEfficiency { get; set; }
        public double CacheHitRatio { get; set; }
        public double ConfigurationOptimization { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    public class CacheHotspot
    {
        public string Identifier { get; set; }
        public HotspotType Type { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public long MemoryUsage { get; set; }
        public int AccessFrequency { get; set; }
        public double ImpactScore { get; set; }
        public string Description { get; set; }
        public List<string> Recommendations { get; set; } = new();
    }

    public enum HotspotType
    {
        HighMemoryUsage,
        LowHitRatio,
        FrequentAccess,
        LongDuration,
        IneffectiveConfiguration
    }

    public class CacheInefficiency
    {
        public string Type { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public string Description { get; set; }
        public double ImpactScore { get; set; }
        public long WastedMemory { get; set; }
        public string Recommendation { get; set; }
        public double PotentialSavings { get; set; }
    }

    public class CachePrediction
    {
        public DateTime PredictionTime { get; set; }
        public TimeSpan ForecastPeriod { get; set; }
        public long PredictedMemoryUsage { get; set; }
        public int PredictedKeyCount { get; set; }
        public double ConfidenceLevel { get; set; }
        public List<TenantPrediction> TenantPredictions { get; set; } = new();
        public List<string> Assumptions { get; set; } = new();
        public List<string> RiskFactors { get; set; } = new();
    }

    public class TenantPrediction
    {
        public int TenantId { get; set; }
        public long PredictedMemoryUsage { get; set; }
        public int PredictedKeyCount { get; set; }
        public double GrowthRate { get; set; }
        public string RiskLevel { get; set; }
    }

    public class CacheOptimizationRecommendation
    {
        public string Id { get; set; }
        public OptimizationType Type { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public int Priority { get; set; }
        public double ImpactScore { get; set; }
        public long EstimatedMemorySavings { get; set; }
        public double EstimatedPerformanceGain { get; set; }
        public string Implementation { get; set; }
        public List<string> Prerequisites { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public enum OptimizationType
    {
        DurationAdjustment,
        MemoryLimit,
        EvictionPolicy,
        ConfigurationChange,
        ArchitecturalChange,
        DataStructureOptimization
    }

    public class CacheHealthScore
    {
        public double OverallScore { get; set; }
        public DateTime CalculatedAt { get; set; }
        public HealthScoreBreakdown Breakdown { get; set; }
        public List<HealthIssue> Issues { get; set; } = new();
        public List<HealthRecommendation> Recommendations { get; set; } = new();
        public string HealthLevel { get; set; }
    }

    public class HealthScoreBreakdown
    {
        public double MemoryEfficiency { get; set; }
        public double HitRatioScore { get; set; }
        public double ConfigurationScore { get; set; }
        public double PerformanceScore { get; set; }
        public double StabilityScore { get; set; }
    }

    public class HealthIssue
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public string Severity { get; set; }
        public double Impact { get; set; }
        public string Recommendation { get; set; }
    }

    public class HealthRecommendation
    {
        public string Action { get; set; }
        public string Reason { get; set; }
        public int Priority { get; set; }
        public double ExpectedImprovement { get; set; }
    }

    public class CacheOperation
    {
        public DateTime Timestamp { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public string OperationType { get; set; }
        public string CacheKey { get; set; }
        public bool IsHit { get; set; }
        public long ResponseTimeMs { get; set; }
        public long MemoryImpact { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class CacheConfigurationAnalysis
    {
        public DateTime AnalysisTime { get; set; }
        public List<ConfigurationIssue> Issues { get; set; } = new();
        public List<ConfigurationRecommendation> Recommendations { get; set; } = new();
        public Dictionary<string, EntityConfigAnalysis> EntityAnalysis { get; set; } = new();
        public double OptimizationScore { get; set; }
    }

    public class ConfigurationIssue
    {
        public string EntityName { get; set; }
        public string IssueType { get; set; }
        public string Description { get; set; }
        public string Severity { get; set; }
        public double Impact { get; set; }
    }

    public class ConfigurationRecommendation
    {
        public string EntityName { get; set; }
        public string Parameter { get; set; }
        public object CurrentValue { get; set; }
        public object RecommendedValue { get; set; }
        public string Reason { get; set; }
        public double ExpectedImprovement { get; set; }
    }

    public class EntityConfigAnalysis
    {
        public string EntityName { get; set; }
        public int CurrentDuration { get; set; }
        public int RecommendedDuration { get; set; }
        public double MemoryEfficiency { get; set; }
        public double HitRatio { get; set; }
        public string[] CurrentTags { get; set; }
        public string[] RecommendedTags { get; set; }
        public bool IsOptimal { get; set; }
    }

    public class EntityCacheAnalysis
    {
        public string EntityName { get; set; }
        public long TotalMemoryUsage { get; set; }
        public int TotalKeys { get; set; }
        public double AverageHitRatio { get; set; }
        public int TenantsUsing { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public double EfficiencyScore { get; set; }
        public List<TenantEntityUsage> TenantUsages { get; set; } = new();
        public EntityOptimizationPotential OptimizationPotential { get; set; }
    }

    public class TenantEntityUsage
    {
        public int TenantId { get; set; }
        public long MemoryUsage { get; set; }
        public int KeyCount { get; set; }
        public double HitRatio { get; set; }
        public int AccessCount { get; set; }
    }

    public class EntityOptimizationPotential
    {
        public double MemorySavingsPotential { get; set; }
        public double PerformanceGainPotential { get; set; }
        public List<string> OptimizationActions { get; set; } = new();
        public int Priority { get; set; }
    }
}
