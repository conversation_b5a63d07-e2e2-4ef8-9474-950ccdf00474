using Core.CrossCuttingConcerns.Caching.Isolation;
using Core.CrossCuttingConcerns.Caching.Isolation.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CacheIsolationController : ControllerBase
    {
        private readonly ITenantCacheIsolationManager _isolationManager;

        public CacheIsolationController(ITenantCacheIsolationManager isolationManager)
        {
            _isolationManager = isolationManager;
        }

        // Tenant quota management
        [HttpGet("quota/{tenantId}")]
        public async Task<IActionResult> GetTenantQuota(int tenantId)
        {
            try
            {
                var quota = await _isolationManager.GetTenantQuotaAsync(tenantId);
                return Ok(new { success = true, data = quota, message = "Tenant quota başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Quota alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("quota/{tenantId}")]
        public async Task<IActionResult> SetTenantQuota(int tenantId, [FromBody] TenantCacheQuota quota)
        {
            try
            {
                await _isolationManager.SetTenantQuotaAsync(tenantId, quota);
                return Ok(new { success = true, message = "Tenant quota başarıyla güncellendi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Quota güncellenirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("quota/{tenantId}/check")]
        public async Task<IActionResult> CheckTenantQuota(int tenantId, [FromQuery] long additionalMemory)
        {
            try
            {
                var canAdd = await _isolationManager.CheckTenantQuotaAsync(tenantId, additionalMemory);
                return Ok(new { success = true, data = canAdd, message = "Quota kontrolü tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Quota kontrolü yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("quotas")]
        public async Task<IActionResult> GetAllTenantQuotas()
        {
            try
            {
                var quotas = await _isolationManager.GetAllTenantQuotasAsync();
                return Ok(new { success = true, data = quotas, message = "Tüm tenant quota'ları başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Quota'lar alınırken hata oluştu: {ex.Message}" });
            }
        }

        // Memory pressure handling
        [HttpPost("memory-pressure/{level}")]
        public async Task<IActionResult> HandleMemoryPressure(MemoryPressureLevel level)
        {
            try
            {
                var response = await _isolationManager.HandleMemoryPressureAsync(level);
                return Ok(new { success = true, data = response, message = "Memory pressure başarıyla işlendi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Memory pressure işlenirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("eviction-candidates/{tenantId}")]
        public async Task<IActionResult> GetEvictionCandidates(int tenantId, [FromQuery] long targetMemoryReduction)
        {
            try
            {
                var candidates = await _isolationManager.GetEvictionCandidatesAsync(tenantId, targetMemoryReduction);
                return Ok(new { success = true, data = candidates, message = "Eviction candidate'ları başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Eviction candidate'ları alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("evict")]
        public async Task<IActionResult> EvictCacheItems([FromBody] EvictionCandidate[] candidates)
        {
            try
            {
                var result = await _isolationManager.EvictCacheItemsAsync(candidates.ToList());
                return Ok(new { success = true, data = result, message = "Cache eviction başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Cache eviction yapılırken hata oluştu: {ex.Message}" });
            }
        }

        // Tenant isolation
        [HttpGet("isolation/{tenantId}")]
        public async Task<IActionResult> GetTenantIsolationStatus(int tenantId)
        {
            try
            {
                var status = await _isolationManager.GetTenantIsolationStatusAsync(tenantId);
                return Ok(new { success = true, data = status, message = "Isolation durumu başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Isolation durumu alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("isolation/{tenantId}/isolate")]
        public async Task<IActionResult> IsolateTenant(int tenantId, [FromBody] string reason)
        {
            try
            {
                await _isolationManager.IsolateTenantAsync(tenantId, reason);
                return Ok(new { success = true, message = "Tenant başarıyla izole edildi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Tenant izole edilirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("isolation/{tenantId}/restore")]
        public async Task<IActionResult> RestoreTenant(int tenantId)
        {
            try
            {
                await _isolationManager.RestoreTenantAsync(tenantId);
                return Ok(new { success = true, message = "Tenant başarıyla restore edildi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Tenant restore edilirken hata oluştu: {ex.Message}" });
            }
        }

        // Allocation strategies
        [HttpGet("allocation/{tenantId}")]
        public async Task<IActionResult> GetOptimalAllocationStrategy(int tenantId)
        {
            try
            {
                var strategy = await _isolationManager.GetOptimalAllocationStrategyAsync(tenantId);
                return Ok(new { success = true, data = strategy, message = "Optimal allocation stratejisi başarıyla oluşturuldu" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Allocation stratejisi oluşturulurken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("allocation/{tenantId}")]
        public async Task<IActionResult> ApplyAllocationStrategy(int tenantId, [FromBody] CacheAllocationStrategy strategy)
        {
            try
            {
                await _isolationManager.ApplyAllocationStrategyAsync(tenantId, strategy);
                return Ok(new { success = true, message = "Allocation stratejisi başarıyla uygulandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Allocation stratejisi uygulanırken hata oluştu: {ex.Message}" });
            }
        }

        // Alerts
        [HttpGet("alerts")]
        public async Task<IActionResult> GetActiveAlerts()
        {
            try
            {
                var alerts = await _isolationManager.GetActiveAlertsAsync();
                return Ok(new { success = true, data = alerts, message = "Aktif alert'ler başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Alert'ler alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("alerts/{alertId}/resolve")]
        public async Task<IActionResult> ResolveAlert(string alertId)
        {
            try
            {
                await _isolationManager.ResolveAlertAsync(alertId);
                return Ok(new { success = true, message = "Alert başarıyla çözüldü" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Alert çözülürken hata oluştu: {ex.Message}" });
            }
        }

        // Auto-scaling
        [HttpGet("autoscaling/{tenantId}/recommendation")]
        public async Task<IActionResult> GetAutoScalingRecommendation(int tenantId)
        {
            try
            {
                var recommendation = await _isolationManager.GetAutoScalingRecommendationAsync(tenantId);
                return Ok(new { success = true, data = recommendation, message = "Auto-scaling önerisi başarıyla oluşturuldu" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Auto-scaling önerisi oluşturulurken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("autoscaling/{tenantId}/scale")]
        public async Task<IActionResult> AutoScaleTenantCache(int tenantId)
        {
            try
            {
                var result = await _isolationManager.AutoScaleTenantCacheAsync(tenantId);
                return Ok(new { success = true, data = result, message = "Auto-scaling başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Auto-scaling yapılırken hata oluştu: {ex.Message}" });
            }
        }

        // Performance
        [HttpGet("performance/{tenantId}")]
        public async Task<IActionResult> GetTenantPerformanceProfile(int tenantId)
        {
            try
            {
                var profile = await _isolationManager.GetTenantPerformanceProfileAsync(tenantId);
                return Ok(new { success = true, data = profile, message = "Performance profili başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Performance profili alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("performance/{tenantId}/optimize")]
        public async Task<IActionResult> OptimizeTenantCache(int tenantId)
        {
            try
            {
                await _isolationManager.OptimizeTenantCacheAsync(tenantId);
                return Ok(new { success = true, message = "Tenant cache başarıyla optimize edildi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Cache optimize edilirken hata oluştu: {ex.Message}" });
            }
        }

        // Emergency operations
        [HttpPost("emergency/memory-pressure")]
        public async Task<IActionResult> HandleEmergencyMemoryPressure()
        {
            try
            {
                var response = await _isolationManager.HandleEmergencyMemoryPressureAsync();
                return Ok(new { success = true, data = response, message = "Emergency memory pressure başarıyla işlendi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Emergency işlem yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("emergency/force-evict/{tenantId}")]
        public async Task<IActionResult> ForceEvictTenantCache(int tenantId, [FromQuery] double percentage = 0.5)
        {
            try
            {
                var success = await _isolationManager.ForceEvictTenantCacheAsync(tenantId, percentage);
                return Ok(new { success = true, data = success, message = "Force eviction başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Force eviction yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("emergency/recover")]
        public async Task<IActionResult> RecoverFromMemoryExhaustion()
        {
            try
            {
                var result = await _isolationManager.RecoverFromMemoryExhaustionAsync();
                return Ok(new { success = true, data = result, message = "System recovery başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"System recovery yapılırken hata oluştu: {ex.Message}" });
            }
        }

        // Global operations
        [HttpPost("global/optimize")]
        public async Task<IActionResult> OptimizeGlobalCacheAllocation()
        {
            try
            {
                var result = await _isolationManager.OptimizeGlobalCacheAllocationAsync();
                return Ok(new { success = true, data = result, message = "Global cache optimization başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Global optimization yapılırken hata oluştu: {ex.Message}" });
            }
        }
    }
}
