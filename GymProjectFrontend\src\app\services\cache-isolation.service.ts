import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { SingleResponseModel, ResponseModel } from '../models/responseModel';

// Isolation Models
export interface TenantCacheQuota {
  tenantId: number;
  maxMemoryBytes: number;
  maxKeys: number;
  currentMemoryUsage: number;
  currentKeyCount: number;
  memoryUtilizationPercentage: number;
  keyUtilizationPercentage: number;
  status: 'Normal' | 'Warning' | 'Critical' | 'Exceeded' | 'Isolated';
  lastUpdated: string;
  enforcementPolicy: QuotaEnforcementPolicy;
  violations: QuotaViolation[];
  autoScaling: AutoScalingSettings;
}

export interface QuotaEnforcementPolicy {
  warningThreshold: number;
  criticalThreshold: number;
  maxThreshold: number;
  evictionPolicy: 'LRU' | 'LFU' | 'TTL' | 'Size' | 'Priority' | 'Hybrid';
  allowTemporaryOverage: boolean;
  temporaryOverageWindow: string;
  autoEvictionEnabled: boolean;
  isolationOnViolation: boolean;
}

export interface QuotaViolation {
  id: string;
  timestamp: string;
  type: 'MemoryQuotaExceeded' | 'KeyCountExceeded' | 'RapidGrowth' | 'SustainedOverage' | 'PerformanceDegradation';
  excessMemory: number;
  excessKeys: number;
  description: string;
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  isResolved: boolean;
  resolvedAt?: string;
  resolution?: string;
}

export interface AutoScalingSettings {
  enabled: boolean;
  scaleUpThreshold: number;
  scaleDownThreshold: number;
  scaleUpFactor: number;
  scaleDownFactor: number;
  minMemoryBytes: number;
  maxMemoryBytes: number;
  cooldownPeriod: string;
  lastScaleOperation?: string;
}

export interface MemoryPressureResponse {
  level: 'None' | 'Low' | 'Medium' | 'High' | 'Critical' | 'Emergency';
  responseTime: string;
  actionsPerformed: PressureAction[];
  memoryFreed: number;
  itemsEvicted: number;
  success: boolean;
  message: string;
  responseDuration: string;
}

export interface PressureAction {
  actionType: string;
  tenantId: number;
  entityName: string;
  memoryFreed: number;
  itemsAffected: number;
  success: boolean;
  details: string;
}

export interface EvictionCandidate {
  cacheKey: string;
  tenantId: number;
  entityName: string;
  memorySize: number;
  createdAt: string;
  lastAccessedAt: string;
  accessCount: number;
  evictionScore: number;
  reason: 'LeastRecentlyUsed' | 'LeastFrequentlyUsed' | 'Expired' | 'LargestSize' | 'LowestPriority' | 'QuotaViolation' | 'MemoryPressure' | 'ManualEviction';
  metadata: { [key: string]: any };
}

export interface EvictionResult {
  totalCandidates: number;
  successfulEvictions: number;
  failedEvictions: number;
  totalMemoryFreed: number;
  evictionDuration: string;
  errors: EvictionError[];
  memoryFreedPerTenant: { [key: number]: number };
}

export interface EvictionError {
  cacheKey: string;
  errorMessage: string;
}

export interface TenantCacheIsolationStatus {
  tenantId: number;
  isIsolated: boolean;
  isolationStartTime?: string;
  isolationReason?: string;
  level: 'None' | 'ReadOnly' | 'Limited' | 'Quarantined' | 'Suspended';
  restrictions: IsolationRestriction[];
  metrics: IsolationMetrics;
  estimatedRestoreTime?: string;
  restoreConditions: string[];
}

export interface IsolationRestriction {
  type: string;
  description: string;
  isActive: boolean;
  expiresAt?: string;
}

export interface IsolationMetrics {
  memoryUsageBeforeIsolation: number;
  memoryUsageAfterIsolation: number;
  performanceImpact: number;
  blockedOperations: number;
  isolationDuration: string;
}

export interface CacheAllocationStrategy {
  tenantId: number;
  strategyName: string;
  method: 'EqualDistribution' | 'UsageBasedDistribution' | 'PriorityBasedDistribution' | 'PerformanceOptimized' | 'MemoryOptimized' | 'Adaptive';
  entityAllocations: { [key: string]: EntityAllocation };
  priority: 'Low' | 'Normal' | 'High' | 'Critical';
  efficiencyScore: number;
  createdAt: string;
  isActive: boolean;
  constraints: AllocationConstraints;
}

export interface EntityAllocation {
  entityName: string;
  allocatedMemory: number;
  allocatedKeys: number;
  priority: number;
  recommendedDuration: string;
  recommendedTags: string[];
  isOptimal: boolean;
}

export interface AllocationConstraints {
  minMemoryPerEntity: number;
  maxMemoryPerEntity: number;
  minKeysPerEntity: number;
  maxKeysPerEntity: number;
  restrictedEntities: string[];
  priorityEntities: string[];
}

export interface TenantCacheAlert {
  id: string;
  tenantId: number;
  type: 'QuotaWarning' | 'QuotaExceeded' | 'MemoryPressure' | 'PerformanceDegradation' | 'IsolationTriggered' | 'AutoScalingFailed' | 'ConfigurationIssue' | 'SystemAlert';
  severity: 'Info' | 'Warning' | 'Error' | 'Critical';
  title: string;
  message: string;
  createdAt: string;
  resolvedAt?: string;
  isResolved: boolean;
  resolvedBy?: string;
  resolution?: string;
  metadata: { [key: string]: any };
  suggestedActions: AlertAction[];
}

export interface AlertAction {
  actionType: string;
  description: string;
  isAutomated: boolean;
  parameters: { [key: string]: any };
}

export interface AutoScalingRecommendation {
  tenantId: number;
  direction: 'None' | 'Up' | 'Down';
  recommendedFactor: number;
  currentQuota: number;
  recommendedQuota: number;
  reasoning: string;
  confidenceLevel: number;
  prerequisites: string[];
  riskFactors: string[];
  validUntil: string;
}

export interface AutoScalingResult {
  success: boolean;
  direction: 'None' | 'Up' | 'Down';
  previousQuota: number;
  newQuota: number;
  scalingFactor: number;
  reason: string;
  scaledAt: string;
  warnings: string[];
  errorMessage?: string;
}

export interface TenantPerformanceProfile {
  tenantId: number;
  profiledAt: string;
  profilePeriod: string;
  metrics: PerformanceMetrics;
  bottlenecks: PerformanceBottleneck[];
  recommendations: PerformanceRecommendation[];
  overallScore: number;
  category: 'Excellent' | 'Good' | 'Average' | 'Poor' | 'Critical';
}

export interface PerformanceMetrics {
  averageHitRatio: number;
  averageResponseTime: number;
  memoryEfficiency: number;
  totalOperations: number;
  throughputPerSecond: number;
  errorRate: number;
  entityPerformance: { [key: string]: number };
}

export interface PerformanceBottleneck {
  type: string;
  entityName: string;
  description: string;
  impactScore: number;
  causes: string[];
  solutions: string[];
}

export interface PerformanceRecommendation {
  action: string;
  target: string;
  description: string;
  expectedImprovement: number;
  priority: number;
  parameters: { [key: string]: any };
}

export interface EmergencyResponse {
  responseTime: string;
  triggerLevel: 'None' | 'Low' | 'Medium' | 'High' | 'Critical' | 'Emergency';
  actionsPerformed: EmergencyAction[];
  totalMemoryFreed: number;
  systemStabilized: boolean;
  responseDuration: string;
  status: string;
  warnings: string[];
}

export interface EmergencyAction {
  actionType: string;
  target: string;
  memoryFreed: number;
  success: boolean;
  details: string;
  duration: string;
}

export interface SystemRecoveryResult {
  recoverySuccessful: boolean;
  recoveryStartTime: string;
  recoveryEndTime?: string;
  recoveryDuration: string;
  steps: RecoveryStep[];
  postRecoveryStatus: 'Healthy' | 'Degraded' | 'Unstable' | 'Critical' | 'Recovering';
  remainingIssues: string[];
  preventiveMeasures: string[];
}

export interface RecoveryStep {
  stepName: string;
  description: string;
  success: boolean;
  duration: string;
  result: string;
  warnings: string[];
}

@Injectable({
  providedIn: 'root'
})
export class CacheIsolationService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  // Tenant quota management
  getTenantQuota(tenantId: number): Observable<SingleResponseModel<TenantCacheQuota>> {
    return this.httpClient.get<SingleResponseModel<TenantCacheQuota>>(
      `${this.apiUrl}cacheisolation/quota/${tenantId}`
    );
  }

  setTenantQuota(tenantId: number, quota: TenantCacheQuota): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/quota/${tenantId}`, quota
    );
  }

  checkTenantQuota(tenantId: number, additionalMemory: number): Observable<SingleResponseModel<boolean>> {
    return this.httpClient.get<SingleResponseModel<boolean>>(
      `${this.apiUrl}cacheisolation/quota/${tenantId}/check?additionalMemory=${additionalMemory}`
    );
  }

  getAllTenantQuotas(): Observable<SingleResponseModel<{ [key: number]: TenantCacheQuota }>> {
    return this.httpClient.get<SingleResponseModel<{ [key: number]: TenantCacheQuota }>>(
      `${this.apiUrl}cacheisolation/quotas`
    );
  }

  // Memory pressure handling
  handleMemoryPressure(level: string): Observable<SingleResponseModel<MemoryPressureResponse>> {
    return this.httpClient.post<SingleResponseModel<MemoryPressureResponse>>(
      `${this.apiUrl}cacheisolation/memory-pressure/${level}`, {}
    );
  }

  getEvictionCandidates(tenantId: number, targetMemoryReduction: number): Observable<SingleResponseModel<EvictionCandidate[]>> {
    return this.httpClient.get<SingleResponseModel<EvictionCandidate[]>>(
      `${this.apiUrl}cacheisolation/eviction-candidates/${tenantId}?targetMemoryReduction=${targetMemoryReduction}`
    );
  }

  evictCacheItems(candidates: EvictionCandidate[]): Observable<SingleResponseModel<EvictionResult>> {
    return this.httpClient.post<SingleResponseModel<EvictionResult>>(
      `${this.apiUrl}cacheisolation/evict`, candidates
    );
  }

  // Tenant isolation
  getTenantIsolationStatus(tenantId: number): Observable<SingleResponseModel<TenantCacheIsolationStatus>> {
    return this.httpClient.get<SingleResponseModel<TenantCacheIsolationStatus>>(
      `${this.apiUrl}cacheisolation/isolation/${tenantId}`
    );
  }

  isolateTenant(tenantId: number, reason: string): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/isolation/${tenantId}/isolate`, reason
    );
  }

  restoreTenant(tenantId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/isolation/${tenantId}/restore`, {}
    );
  }

  // Allocation strategies
  getOptimalAllocationStrategy(tenantId: number): Observable<SingleResponseModel<CacheAllocationStrategy>> {
    return this.httpClient.get<SingleResponseModel<CacheAllocationStrategy>>(
      `${this.apiUrl}cacheisolation/allocation/${tenantId}`
    );
  }

  applyAllocationStrategy(tenantId: number, strategy: CacheAllocationStrategy): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/allocation/${tenantId}`, strategy
    );
  }

  // Alerts
  getActiveAlerts(): Observable<SingleResponseModel<TenantCacheAlert[]>> {
    return this.httpClient.get<SingleResponseModel<TenantCacheAlert[]>>(
      `${this.apiUrl}cacheisolation/alerts`
    );
  }

  resolveAlert(alertId: string): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/alerts/${alertId}/resolve`, {}
    );
  }

  // Auto-scaling
  getAutoScalingRecommendation(tenantId: number): Observable<SingleResponseModel<AutoScalingRecommendation>> {
    return this.httpClient.get<SingleResponseModel<AutoScalingRecommendation>>(
      `${this.apiUrl}cacheisolation/autoscaling/${tenantId}/recommendation`
    );
  }

  autoScaleTenantCache(tenantId: number): Observable<SingleResponseModel<AutoScalingResult>> {
    return this.httpClient.post<SingleResponseModel<AutoScalingResult>>(
      `${this.apiUrl}cacheisolation/autoscaling/${tenantId}/scale`, {}
    );
  }

  // Performance
  getTenantPerformanceProfile(tenantId: number): Observable<SingleResponseModel<TenantPerformanceProfile>> {
    return this.httpClient.get<SingleResponseModel<TenantPerformanceProfile>>(
      `${this.apiUrl}cacheisolation/performance/${tenantId}`
    );
  }

  optimizeTenantCache(tenantId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/performance/${tenantId}/optimize`, {}
    );
  }

  // Emergency operations
  handleEmergencyMemoryPressure(): Observable<SingleResponseModel<EmergencyResponse>> {
    return this.httpClient.post<SingleResponseModel<EmergencyResponse>>(
      `${this.apiUrl}cacheisolation/emergency/memory-pressure`, {}
    );
  }

  forceEvictTenantCache(tenantId: number, percentage: number = 0.5): Observable<SingleResponseModel<boolean>> {
    return this.httpClient.post<SingleResponseModel<boolean>>(
      `${this.apiUrl}cacheisolation/emergency/force-evict/${tenantId}?percentage=${percentage}`, {}
    );
  }

  recoverFromMemoryExhaustion(): Observable<SingleResponseModel<SystemRecoveryResult>> {
    return this.httpClient.post<SingleResponseModel<SystemRecoveryResult>>(
      `${this.apiUrl}cacheisolation/emergency/recover`, {}
    );
  }

  // Global operations
  optimizeGlobalCacheAllocation(): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}cacheisolation/global/optimize`, {}
    );
  }

  // Utility methods
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getQuotaStatusColor(status: string): string {
    switch (status) {
      case 'Normal': return 'text-success';
      case 'Warning': return 'text-warning';
      case 'Critical': return 'text-danger';
      case 'Exceeded': return 'text-danger fw-bold';
      case 'Isolated': return 'text-dark bg-warning';
      default: return 'text-secondary';
    }
  }

  getIsolationLevelColor(level: string): string {
    switch (level) {
      case 'None': return 'text-success';
      case 'ReadOnly': return 'text-info';
      case 'Limited': return 'text-warning';
      case 'Quarantined': return 'text-danger';
      case 'Suspended': return 'text-danger fw-bold';
      default: return 'text-secondary';
    }
  }

  getAlertSeverityColor(severity: string): string {
    switch (severity) {
      case 'Info': return 'text-info';
      case 'Warning': return 'text-warning';
      case 'Error': return 'text-danger';
      case 'Critical': return 'text-danger fw-bold';
      default: return 'text-secondary';
    }
  }

  getPerformanceCategoryColor(category: string): string {
    switch (category) {
      case 'Excellent': return 'text-success';
      case 'Good': return 'text-info';
      case 'Average': return 'text-warning';
      case 'Poor': return 'text-danger';
      case 'Critical': return 'text-danger fw-bold';
      default: return 'text-secondary';
    }
  }
}
