using Core.CrossCuttingConcerns.Caching.Analytics;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CacheAnalyticsController : ControllerBase
    {
        private readonly ICacheAnalyticsService _cacheAnalyticsService;

        public CacheAnalyticsController(ICacheAnalyticsService cacheAnalyticsService)
        {
            _cacheAnalyticsService = cacheAnalyticsService;
        }

        [HttpGet("snapshot")]
        public async Task<IActionResult> GetCurrentSnapshot()
        {
            try
            {
                var snapshot = await _cacheAnalyticsService.GetCurrentSnapshotAsync();
                return Ok(new { success = true, data = snapshot, message = "Cache snapshot başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Cache snapshot alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("usage-patterns/{tenantId}")]
        public async Task<IActionResult> GetUsagePatterns(int tenantId, [FromQuery] int hours = 24)
        {
            try
            {
                var timeRange = TimeSpan.FromHours(hours);
                var patterns = await _cacheAnalyticsService.GetUsagePatternsAsync(tenantId, timeRange);
                return Ok(new { success = true, data = patterns, message = "Kullanım desenleri başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Kullanım desenleri alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("memory-analysis")]
        public async Task<IActionResult> GetMemoryUsageAnalysis()
        {
            try
            {
                var analysis = await _cacheAnalyticsService.GetMemoryUsageAnalysisAsync();
                return Ok(new { success = true, data = analysis, message = "Bellek analizi başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Bellek analizi yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("historical")]
        public async Task<IActionResult> GetHistoricalData([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-7);
                var end = endDate ?? DateTime.UtcNow;
                
                var historicalData = await _cacheAnalyticsService.GetHistoricalDataAsync(start, end);
                return Ok(new { success = true, data = historicalData, message = "Geçmiş veriler başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Geçmiş veriler alınırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("tenant-analysis/{tenantId}")]
        public async Task<IActionResult> GetTenantAnalysis(int tenantId, [FromQuery] int hours = 24)
        {
            try
            {
                var timeRange = TimeSpan.FromHours(hours);
                var analysis = await _cacheAnalyticsService.GetTenantAnalysisAsync(tenantId, timeRange);
                return Ok(new { success = true, data = analysis, message = "Tenant analizi başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Tenant analizi yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("hotspots")]
        public async Task<IActionResult> GetCacheHotspots()
        {
            try
            {
                var hotspots = await _cacheAnalyticsService.GetCacheHotspotsAsync();
                return Ok(new { success = true, data = hotspots, message = "Cache hotspot'ları başarıyla tespit edildi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Hotspot'lar tespit edilirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("inefficiencies")]
        public async Task<IActionResult> GetInefficiencies()
        {
            try
            {
                var inefficiencies = await _cacheAnalyticsService.GetInefficienciesAsync();
                return Ok(new { success = true, data = inefficiencies, message = "Cache verimsizlikleri başarıyla tespit edildi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Verimsizlikler tespit edilirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("predictions")]
        public async Task<IActionResult> GetMemoryPredictions([FromQuery] int forecastHours = 24)
        {
            try
            {
                var forecastPeriod = TimeSpan.FromHours(forecastHours);
                var prediction = await _cacheAnalyticsService.PredictMemoryUsageAsync(forecastPeriod);
                return Ok(new { success = true, data = prediction, message = "Bellek kullanım tahmini başarıyla oluşturuldu" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Tahmin oluşturulurken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("recommendations")]
        public async Task<IActionResult> GetOptimizationRecommendations()
        {
            try
            {
                var recommendations = await _cacheAnalyticsService.GetOptimizationRecommendationsAsync();
                return Ok(new { success = true, data = recommendations, message = "Optimizasyon önerileri başarıyla oluşturuldu" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Öneriler oluşturulurken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("health-score")]
        public async Task<IActionResult> GetCacheHealthScore()
        {
            try
            {
                var healthScore = await _cacheAnalyticsService.GetCacheHealthScoreAsync();
                return Ok(new { success = true, data = healthScore, message = "Cache sağlık skoru başarıyla hesaplandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Sağlık skoru hesaplanırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("configuration-analysis")]
        public async Task<IActionResult> GetConfigurationAnalysis()
        {
            try
            {
                var analysis = await _cacheAnalyticsService.AnalyzeCacheConfigurationAsync();
                return Ok(new { success = true, data = analysis, message = "Konfigürasyon analizi başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Konfigürasyon analizi yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("entity-analysis")]
        public async Task<IActionResult> GetEntityAnalysis()
        {
            try
            {
                var analysis = await _cacheAnalyticsService.GetEntityAnalysisAsync();
                return Ok(new { success = true, data = analysis, message = "Entity analizi başarıyla tamamlandı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Entity analizi yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("record-snapshot")]
        public async Task<IActionResult> RecordMemorySnapshot()
        {
            try
            {
                await _cacheAnalyticsService.RecordMemorySnapshotAsync();
                return Ok(new { success = true, message = "Bellek snapshot'ı başarıyla kaydedildi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Snapshot kaydedilirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupOldData([FromQuery] int retentionDays = 7)
        {
            try
            {
                var retentionPeriod = TimeSpan.FromDays(retentionDays);
                await _cacheAnalyticsService.CleanupOldDataAsync(retentionPeriod);
                return Ok(new { success = true, message = "Eski veriler başarıyla temizlendi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Veri temizliği yapılırken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("dashboard")]
        public async Task<IActionResult> GetAnalyticsDashboard()
        {
            try
            {
                var snapshot = await _cacheAnalyticsService.GetCurrentSnapshotAsync();
                var memoryAnalysis = await _cacheAnalyticsService.GetMemoryUsageAnalysisAsync();
                var healthScore = await _cacheAnalyticsService.GetCacheHealthScoreAsync();
                var hotspots = await _cacheAnalyticsService.GetCacheHotspotsAsync();
                var recommendations = await _cacheAnalyticsService.GetOptimizationRecommendationsAsync();

                var dashboard = new
                {
                    snapshot,
                    memoryAnalysis,
                    healthScore,
                    hotspots = hotspots.Take(5),
                    recommendations = recommendations.Take(3)
                };

                return Ok(new { success = true, data = dashboard, message = "Analytics dashboard başarıyla yüklendi" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Dashboard yüklenirken hata oluştu: {ex.Message}" });
            }
        }

        [HttpGet("real-time-metrics")]
        public async Task<IActionResult> GetRealTimeMetrics()
        {
            try
            {
                var snapshot = await _cacheAnalyticsService.GetCurrentSnapshotAsync();
                var memoryAnalysis = await _cacheAnalyticsService.GetMemoryUsageAnalysisAsync();

                var realTimeMetrics = new
                {
                    timestamp = DateTime.UtcNow,
                    memoryUsage = snapshot.CacheMemoryUsage,
                    memoryPressure = memoryAnalysis.MemoryPressurePercentage,
                    pressureLevel = memoryAnalysis.PressureLevel,
                    totalKeys = snapshot.TotalKeys,
                    activeTenants = snapshot.ActiveTenants,
                    hitRatio = snapshot.OverallHitRatio,
                    trend = memoryAnalysis.Trend?.Direction.ToString() ?? "Stable"
                };

                return Ok(new { success = true, data = realTimeMetrics, message = "Real-time metrikler başarıyla alındı" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = $"Real-time metrikler alınırken hata oluştu: {ex.Message}" });
            }
        }
    }
}
